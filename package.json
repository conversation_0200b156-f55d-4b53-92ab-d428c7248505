{"name": "question-bank-move-web", "type": "module", "version": "1.0.0", "description": "question-bank-move-web", "author": "Geega", "scripts": {"dev": "vite --host", "build": "vite build && tsx scripts/post-build.ts", "g:api": "tsx ./scripts/gen-api.ts", "prepare": "husky install", "lint": "eslint --fix"}, "dependencies": {"@geega-components/geega-guc-js-next-sdk": "^3.1.18", "@tce/online-monitoring": "^2.0.9", "@vueuse/core": "^13.5.0", "@vueuse/integrations": "^13.5.0", "@zxing/library": "^0.21.3", "axios": "1.10.0", "dayjs": "^1.11.13", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "pinia": "^3.0.3", "ufo": "^1.6.1", "vant": "^4.9.20", "vue": "^3.5.17", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^4.16.2", "@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@mk/openapi-code-generator": "^1.0.1", "@types/less": "^3.0.8", "@types/lodash-es": "^4.17.12", "@types/node": "24.0.13", "@unocss/eslint-config": "^66.3.3", "@unocss/eslint-plugin": "^66.3.3", "@vitejs/plugin-legacy": "^7.0.0", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/tsconfig": "^0.7.0", "browserslist": "^4.25.1", "core-js": "^3.44.0", "eslint": "^9.30.1", "eslint-plugin-format": "^1.0.1", "happy-dom": "^18.0.1", "husky": "^9.1.7", "less": "^4.3.0", "lint-staged": "^16.1.2", "normalize.css": "^8.0.1", "prettier": "^3.6.2", "rollup": "^4.44.2", "sass": "^1.89.2", "sass-loader": "^16.0.5", "systemjs": "^6.15.1", "terser": "^5.43.1", "tsx": "^4.20.3", "typescript": "^5.8.3", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-svg-component": "^0.12.2", "unplugin-vue-components": "^28.8.0", "unplugin-vue-router": "^0.14.0", "vite": "^7.0.4", "vite-plugin-inspect": "^11.3.0", "vite-plugin-lazy-import": "^1.0.7"}, "lint-staged": {"src/**/*.{ts,tsx,vue}": []}}
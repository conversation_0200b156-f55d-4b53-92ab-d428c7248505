import type { FunctionDirective } from 'vue'
import { gucStore } from '@/integration/guc'
import { defineVuePlugin } from '@/utils/helper'

const permissionDirective: FunctionDirective<HTMLElement, string> = (el, binding) => {
  if (!el) {
    return
  }

  const code = binding.value

  if (!code)
    return

  const hasPermission = gucStore.hasPermission(code)

  if (hasPermission) {
    el.removeAttribute('hidden')
  }
  else {
    el.setAttribute('hidden', '')
  }
}

export default defineVuePlugin((app) => {
  app.directive('permission', permissionDirective)
})

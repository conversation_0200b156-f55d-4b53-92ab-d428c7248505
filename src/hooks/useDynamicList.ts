interface ApiParameter {
  currentPage: number
  pageSize: number
}

export interface UseDynamicListOption<T> {
  pageSize?: number
  api: (parameter: ApiParameter) => Promise<TableResult<T>>
}

interface TableResult<T = unknown> {
  currentPage?: number
  pageSize?: number
  records?: T[]
  totalRows?: string
  [key: string]: any
}

export function useDynamicList<T>(opt: UseDynamicListOption<T>) {
  const state = reactive({
    currentPage: 0,
    pageSize: opt.pageSize || 20,
    finished: false,
    loading: false,
    data: [] as T[],
  })

  const vanListProps = computed(() => {
    return {
      loading: state.loading,
      finished: state.finished,
    }
  })

  return {
    state,
    data: computed(() => state.data),
    props: vanListProps,
    loading: computed(() => state.loading),
    load: loadNextPage,
    reset,
    forceLoadTheFirstPage,
  } as UseDynamicListReturn<T>

  function reset() {
    state.currentPage = 0
    state.data = []
    state.finished = false
  }

  async function loadNextPage() {
    if (state.loading) {
      return
    }

    state.loading = true

    try {
      await _loadNextPage()
    } catch (error) {
      state.finished = true
      throw error
    } finally {
      state.loading = false
    }
  }

  async function _loadNextPage() {
    const params = {
      currentPage: state.currentPage + 1,
      pageSize: state.pageSize,
    }

    const result = await opt.api(params)

    const totalData = [...state.data, ...(result.records || [])]
    state.finished = totalData.length >= (+result.totalRows! || 0)
    state.currentPage = result.currentPage || 0

    state.data = totalData as any[]
  }

  async function forceLoadTheFirstPage() {
    if (state.finished || state.data.length > 0) {
      return
    }

    return loadNextPage()
  }
}

export type UseDynamicListReturn<T> = {
  state: {
    currentPage: number
    pageSize: number
    finished: boolean
    loading: boolean
    data: T[]
  }
  data: ComputedRef<T[]>
  props: ComputedRef<any>
  loading: ComputedRef<boolean>
  load: () => Promise<void>
  reset: () => void
  forceLoadTheFirstPage: () => Promise<void>
}

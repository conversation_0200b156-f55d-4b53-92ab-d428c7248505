import { useUrlSearchParams } from '@vueuse/core';
import { watch } from 'vue';
import { useRouter } from 'vue-router';

export function useRouteQuery<T extends Record<string, any>>() {
  const router = useRouter();
  const urlParams = useUrlSearchParams<T>('hash');

  watch(
    () => urlParams,
    () => {
      const fullPath = router.currentRoute.value.fullPath;
      const origin = 'http://example.com';
      const url = new URL(fullPath, origin);

      url.search = '';

      Object.entries(urlParams).forEach(([key, value]) => {
        if (value != null) {
          url.searchParams.set(key, value);
        }
      });

      const result = url.toString().slice(origin.length);

      router.options.history.replace(result);
    },
    { deep: true, flush: 'post' }
  );

  return urlParams;
}

import { cloneDeep } from 'lodash-es'
import { type UnwrapRef, computed, reactive } from 'vue'

export interface UseAsyncDataOptions<Data = any> {
  resetValue?: Data
}

export function useAsyncData<Fn extends (...args: any[]) => any, Data = Awaited<ReturnType<Fn>>>(
  fn: Fn,
  defaultValue?: Awaited<ReturnType<Fn>>,
  option: UseAsyncDataOptions<Data> = {},
) {
  type Params = Parameters<Fn>

  const state = reactive({
    data: defaultValue as Data,
    loading: false,
  })

  let latestReqId = 0

  async function load(...args: Params) {
    state.loading = true

    try {
      const currentReqId = ++latestReqId
      if (option.resetValue) {
        state.data = option.resetValue as UnwrapRef<Data>
      }

      const res = await fn(...args)

      if (currentReqId === latestReqId) {
        state.data = res || cloneDeep(defaultValue)
      }
    }
    finally {
      state.loading = false
    }
  }

  const api = {
    load,
    data: computed(() => state.data),
    update(data: Data) {
      latestReqId++

      state.data = data as UnwrapRef<Data>
      state.loading = false
    },
    loading: computed(() => state.loading),
  }

  return api
}

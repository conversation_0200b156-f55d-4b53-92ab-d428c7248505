import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import duration from 'dayjs/plugin/duration'
import dayjsCn from 'dayjs/locale/zh-cn'
import type { Plugin, App as VueApp } from 'vue'
import App from './App.vue'
import { router } from './router'
import { registerOnlineMonitor } from './integration/online-monitor'
import { APP_CONFIG } from '@/config'
import 'normalize.css'
import './theme/index.less'
import 'uno.css'

dayjs.extend(quarterOfYear)
dayjs.extend(duration)
dayjs.locale(dayjsCn)

let app: VueApp | null = null

console.log(`[${APP_CONFIG.version}] Build timestamp:`, new Date(__BUILD_TIMESTAMP__).toString())

mountApp()

export async function mountApp() {
  if (app) return

  registerOnlineMonitor()

  app = createApp(App)

  const modules = import.meta.glob<Plugin>('./modules/*.ts', { eager: true, import: 'default' })

  Object.values(modules).forEach((m) => app!.use(m))

  app.use(router)

  await router.isReady()

  app.mount('#app')
}

export function unmountApp() {
  app?.unmount()
  app = null
}

export enum TrainingStatus {
  /**
   * 待训练
   */
  TO_BE = 'TO_BE',
  /**
   * 训练中
   */
  ING = 'ING',
  /**
   * 已完成
   */
  COMPLETED = 'COMPLETED',
  /**
   * 待考核
   */
  TO_BE_CHECK = 'TO_BE_CHECK',
  /**
   * 考核未通过
   */
  FAILED_CHECK = 'FAILED_CHECK',
  /**
   * 考核通过
   */
  PASSED_CHECK = 'PASSED_CHECK',
  /**
   * 考核完成
   */
  CHECKED = 'CHECKED',
}

export const TrainingStatusOptions = [
  {
    label: '待训练',
    value: TrainingStatus.TO_BE,
  },
  {
    label: '训练中',
    value: TrainingStatus.ING,
  },
  {
    label: '已完成',
    value: TrainingStatus.COMPLETED,
  },
  {
    label: '待考核',
    value: TrainingStatus.TO_BE_CHECK,
  },
  {
    label: '考核未通过',
    value: TrainingStatus.FAILED_CHECK,
  },
  {
    label: '考核通过',
    value: TrainingStatus.PASSED_CHECK,
  },
  {
    label: '考核完成',
    value: TrainingStatus.CHECKED,
  },
]

/**
 * 项目类型(1:训练、2:考核、3:比赛)
 */
export enum TrainingProjectType {
  Training = '1',
  Exam = '2',
  Race = '3',
}

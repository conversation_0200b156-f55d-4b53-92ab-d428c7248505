export enum PaperTypeEnum {
  /**
   * 技能检查, 需要考试
   */
  SKILL_INSPECTION = 'SKILL_INSPECTION',
  /**
   * 技能考核
   */
  SKILL_ASSESSMENT = 'SKILL_ASSESSMENT',
}

export const PaperTypeOptions = [
  {
    label: '技能检查',
    value: PaperTypeEnum.SKILL_INSPECTION,
  },
  {
    label: '技能考核',
    value: PaperTypeEnum.SKILL_ASSESSMENT,
  },
]

export enum ExamStatusEnum {
  /**
   * 待考试
   */
  TO_BE = 'TO_BE',

  /**
   * 弃考
   */
  QUIT = 'QUIT',

  /**
   * 合格
   */
  COMPLETED = 'COMPLETED',

  /**
   * 不合格
   */
  UNQUALIFIED = 'UNQUALIFIED',
}

export const ExamStatusOptions = [
  { label: '待考试', value: ExamStatusEnum.TO_BE },
  { label: '弃考', value: ExamStatusEnum.QUIT },
  { label: '合格', value: ExamStatusEnum.COMPLETED },
  { label: '不合格', value: ExamStatusEnum.UNQUALIFIED },
]

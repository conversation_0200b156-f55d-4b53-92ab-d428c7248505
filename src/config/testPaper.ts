/**
 * 显示试卷模式
 */
export enum TEST_PAPER_MODE {
  /**
   * 可操作题，用户在线填写
   */
  ONLINE_WIRTE = '1',
  /**
   * 不可操作题，回显用户答案和显示标准答案
   */
  VIEW_USER_ANSWERS_AND_EXAMPLE = '2',
  /**
   * 不可操作题和显示标准答案
   */
  VIEW_EXAMPLE = '3',
}

/**
 * 试卷题目类型
 */
export enum QUESTION_TYPE {
  /**
   * 填空题
   */
  FILL_IN_THE_BLANK = 'FILL_IN_THE_BLANK',
  /**
   * 单选题
   */
  SINGLE_CHOICE = 'SINGLE_CHOICE',
  /**
   * 判断题
   */
  JUDGMENT = 'JUDGMENT',
  /**
   * 多选题
   */
  MULTIPLE_CHOICE = 'MULTIPLE_CHOICE',
  /**
   * 简答题
   */
  SHORT_ANSWER = 'SHORT_ANSWER',
}

export enum QUESTION_DIFFICULT_DEGREE {
  EASY = '1',
  NORMAL = '2',
  HARD = '3',
}

export const QuestionDifficultOptions = [
  {
    label: '简单',
    value: QUESTION_DIFFICULT_DEGREE.EASY,
    colorType: 'success',
  },
  {
    label: '普通',
    value: QUESTION_DIFFICULT_DEGREE.NORMAL,
    colorType: 'warning',
  },
  {
    label: '困难',
    value: QUESTION_DIFFICULT_DEGREE.HARD,
    colorType: 'error',
  },
]

import type { AxiosRequestConfig } from 'axios'
import { axiosIns } from './axios'

interface APIParameters<URL = string> {
  url: URL
  /**
   * request body
   */
  data?: any
  /**
   * query
   */
  param?: any

  query?: any
}

function get(data: APIParameters): Promise<ArrayBuffer>
function get<T>(data: APIParameters): Promise<ResponseWrapper<T>>
function get<T>(data: APIParameters): Promise<any> {
  const conf: AxiosRequestConfig = {}

  return axiosIns.get<any, ResponseWrapper<T>>(data.url, {
    ...conf,
    params: data.param || data.query,
  })
}

function post(data: APIParameters): Promise<ArrayBuffer>
function post<T>(data: APIParameters): Promise<ResponseWrapper<T>>
function post<T>(data: APIParameters): Promise<any> {
  const conf: AxiosRequestConfig = {}

  return axiosIns.request<any, ResponseWrapper<T>>({
    ...conf,
    method: 'post',
    url: data.url,
    params: data.param || data.query,
    data: data.data,
  })
}

export const httpAdaptor = {
  post,
  get,
  put<T>(data: APIParameters) {
    return axiosIns.request<any, ResponseWrapper<T>>({
      method: 'put',
      url: data.url,
      params: data.param,
      data: data.data,
    })
  },
  delete<T>(data: APIParameters) {
    return axiosIns.delete<any, ResponseWrapper<T>>(data.url, {
      params: data.param,
      data: data.data,
    })
  },
  patch<T>(data: APIParameters) {
    return axiosIns.request<any, ResponseWrapper<T>>({
      method: 'patch',
      url: data.url,
      params: data.param,
      data: data.data,
    })
  },
}

export type BodyWrapper<T> = T extends { data?: any } ? T & T['data'] : T

export type ResponseWrapper<T> = Required<DeconstructResponseType<T>>

export type Required<T> = [T] extends [infer U | undefined | null] ? U : T

export type DeconstructResponseType<T> = T extends { total?: any }
  ? T
  : T extends { result?: any }
    ? T['result']
    : T

import axios from 'axios'
import { showConfirmDialog, showFailToast } from 'vant'
import { RESPONSE_CODE, checkStatusCode, getRequestBaseUrl } from './utils'
import { API_URL } from '@/env'
import { gucStore } from '@/integration/guc'
import { domainConfig } from '@/integration/domain'

export const axiosIns = axios.create({
  baseURL: getRequestBaseUrl(API_URL),
  timeout: 60 * 1000,
})

axiosIns.interceptors.request.use(async (conf) => {
  conf.headers.Authorization = `Bearer ${await gucStore.getToken()}`
  conf.baseURL = domainConfig.value.host

  return conf
})

axiosIns.interceptors.response.use(
  async (resp) => {
    const { config } = resp

    if (!isStandardResponseData(resp.data)) {
      return resp.data
    }

    const { result, msg, code } = resp.data

    // 处理返回值
    if (code !== RESPONSE_CODE.SUCCESS) {
      showFailToast(msg)
      throw resp.data
    }

    if (config.isTransformResponse === false) {
      return resp.data
    }

    return result
  },
  async (error) => {
    const { response, code, message } = error || {}

    const errMsg = String(error)

    if (code === 'ECONNABORTED' && message.includes('timeout')) {
      showFailToast('接口请求超时,请刷新页面重试!')
    } else if (errMsg.includes('Network Error')) {
      showConfirmDialog({
        title: '网络异常',
        message: '请检查您的网络连接是否正常!',
      })
    }

    const msg: string = response?.data?.error?.message ?? ''

    checkStatusCode(response?.status, msg)

    return Promise.reject(error)
    //
  },
)

/**
 * 判断是否是标准的返回结构
 * @param data
 */
function isStandardResponseData(data: any) {
  if (data instanceof ArrayBuffer) return false

  if (data == null) return false

  if (typeof data === 'object' && !('code' in data)) return false

  return true
}

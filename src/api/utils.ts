import { joinURL } from 'ufo'
import { showFailToast } from 'vant'
import { BASE_URL } from '@/env'
import { gucStore } from '@/integration/guc'
/**
 * 兼容 micro app
 *
 * @param pathPrefix
 */
export function getRequestBaseUrl(pathPrefix: string) {
  return joinURL(BASE_URL, pathPrefix)
}

export function checkStatusCode(status: number, msg: string): void {
  const error = showFailToast

  switch (status) {
    case 400:
      error(`${msg}`)
      break
    case 401:
      gucStore.logoutConfirm('用户没有权限（令牌、用户名、密码错误）!')
      break
    case 403:
      gucStore.logoutConfirm('用户得到授权，但是访问是被禁止的。!')
      break
    case 404:
      error('网络请求错误,未找到该资源!')
      break
    case 405:
      error('网络请求错误,请求方法未允许!')
      break
    case 500:
      error('服务器错误,请联系管理员!')
      break
    case 502:
      error('网络错误!')
      break
    default:
  }
}

export enum RESPONSE_CODE {
  SUCCESS = '0000',
}

import type { MaybeRef } from '@vueuse/core'
import type { VNodeProps } from 'vue'

type _GetComponentProps<C extends { $props: any }> = Omit<C['$props'], keyof VNodeProps>

type GetComponentPublicInstance<T> = T extends InstanceType<infer _> ? InstanceType<T> : T

export type GetComponentProps<T> = T extends Component
  ? MaybeRefObject<_GetComponentProps<GetComponentPublicInstance<T>>>
  : never

export type MaybeRefObject<T> = {
  [key in keyof T]: MaybeRef<T[key]>;
}

export interface SimpleOption<T = any> {
  value: T
  label: string
  [key: string]: any
}

export interface Opt<T = any> {
  value: T
  text: string
  [key: string]: any
}

import { toArray } from './helper'

export function treeWalker<T extends object>(
  root: T | T[],
  childrenKey: keyof T,
  cb: (t: T, parent?: T) => void,
) {
  const rootItems = toArray(root)

  rootItems.forEach(item => visit(item))

  function visit(item: T, parent?: T) {
    cb(item, parent)

    //
    ;(item[childrenKey] as T[])?.forEach(child => visit(child, item))
  }
}

export function convertTree<T extends object, U extends { children?: any[] }>(
  root: T | T[],
  childrenKey: keyof T,
  cb: (t: T, parent?: T) => U,
): U[] {
  const rootItems = toArray(root)

  return rootItems.map(item => visit(item))

  function visit(item: T, parent?: T) {
    const newNode = cb(item, parent)

    newNode.children = (item[childrenKey] as T[])?.map(child => visit(child, item))

    return newNode
  }
}

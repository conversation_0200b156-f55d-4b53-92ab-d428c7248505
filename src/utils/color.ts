export interface RGB {
  /**
   * 0-255
   */
  r: number
  /**
   * 0-255
   */
  g: number
  /**
   * 0-255
   */
  b: number
}

export function hexToRgba(hex: string, alpha = 1) {
  hex = hex.replace('#', '')
  if (hex.length !== 6) {
    console.warn('Invalid HEX color')
    return hex
  }
  const r = Number.parseInt(hex.substring(0, 2), 16)
  const g = Number.parseInt(hex.substring(2, 4), 16)
  const b = Number.parseInt(hex.substring(4, 6), 16)
  return `rgb(${r},${g},${b},${alpha})`
}

/**
 * https://www.w3.org/TR/WCAG21/#dfn-contrast-ratio
 *
 * @param c1
 * @param c2
 */
export function getContrastRatio(c1: RGB, c2: RGB) {
  const _l1 = getLuminance(c1)
  const _l2 = getLuminance(c2)

  const [l1, l2] = _l1 > _l2 ? [_l1, _l2] : [_l2, _l1]

  return (l1 + 0.05) / (l2 + 0.05)
}

/**
 * https://www.w3.org/TR/WCAG21/#dfn-relative-luminance
 *
 * @param c
 * @returns [0, 1]
 */
function getLuminance(c: RGB) {
  let { r, g, b } = normalizeRGB(c)

  r = luminance(r)
  g = luminance(g)
  b = luminance(b)

  // L = 0.2126 * R + 0.7152 * G + 0.0722 * B
  return 0.2126 * r + 0.7152 * g + 0.0722 * b
}

/**
 *
 * if RsRGB <= 0.03928 then R = RsRGB/12.92 else R = ((RsRGB+0.055)/1.055) ^ 2.4
 *
 * @param x
 */
function luminance(x: number) {
  return x <= 0.03928 ? x / 12.92 : ((x + 0.055) / 1.055) ** 2.4
}

/**
 *
 * https://www.w3.org/TR/WCAG21/#contrast-enhanced > 7
 *
 * https://www.w3.org/TR/WCAG21/#contrast-minimum > 4.5
 *
 * https://www.w3.org/TR/WCAG21/#non-text-contrast > 3
 * @param contrast
 */
export function getContrastLevel(contrast: number) {
  const level = { AALarge: false, AA: false, AAALarge: false, AAA: false }

  if (contrast > 7) {
    // https://www.w3.org/TR/WCAG21/#contrast-enhanced
    level.AA = true
    level.AAA = true
    level.AALarge = true
    level.AAALarge = true
  }
  else if (contrast > 4.5) {
    // https://www.w3.org/TR/WCAG21/#contrast-minimum
    level.AA = true
    level.AALarge = true
    level.AAALarge = true
  }
  else if (contrast > 3) {
    // https://www.w3.org/TR/WCAG21/#non-text-contrast
    level.AALarge = true
  }

  return level
}

export function normalizeRGB(c: RGB): RGB {
  return {
    r: c.r / 0xFF,
    g: c.g / 0xFF,
    b: c.b / 0xFF,
  }
}

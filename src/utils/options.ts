import type { SimpleOption } from './types'

export function getOption<T>(options: SimpleOption<T>[], value: T) {
  return options.find(n => n.value === value)
}

export function getOptionLabel<T>(options: SimpleOption<T>[], value: T) {
  return options.find(n => n.value === value)?.label
}

export function toSimpleOptions<T extends string | number | boolean>(
  options: T[],
): SimpleOption<T>[] {
  return options.map(n => ({
    label: n.toString(),
    value: n,
  }))
}

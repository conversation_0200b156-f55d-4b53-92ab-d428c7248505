export function uuid(): string {
  // 存储UUID的每个字符的数组
  const s: string[] = []

  // 用于生成十六进制字符的字母表
  const hexDigits: string = `0123456789abcdef`

  // 循环36次，生成UUID的每个字符
  for (let i: number = 0; i < 36; ++i) {
    // 随机选择一个索引作为当前字符的值
    const start: number = Math.floor(Math.random() * 0x10)

    // 将选定的字符添加到数组中
    s[i] = hexDigits.slice(start, start + 1)
  }

  // 设置UUID的特定位置的字符
  s[14] = `4`

  const start = (Number(s[19]) & 0x3) | 0x8
  s[19] = hexDigits.slice(start, start + 1)

  // 在特定位置添加连字符
  s[8] = s[13] = s[18] = s[23] = `-`

  // 将字符数组连接成最终的UUID字符串
  const uuid: string = s.join(``)

  return uuid
}

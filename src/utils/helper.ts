import type { Plugin } from 'vue'

export function defineVuePlugin(plugin: Plugin) {
  return plugin
}

/**
 * 按照从左到右的顺序把所有 listeners 合并成一个 listener
 *
 * @param listeners
 */
export function composeListeners<T extends (...args: any[]) => void>(
  ...listeners: (T | undefined | null)[]
): T {
  const wrapper = ((...args) => {
    listeners.forEach(item => item?.(...args))
  }) as T

  return wrapper
}

/**
 * 按照从右到左的顺序把所有 listeners 合并成一个 listener
 *
 * @param listeners
 */
composeListeners.reverse = <T extends (...args: any[]) => void>(
  ...listeners: (T | undefined | null)[]
): T => composeListeners(...listeners.reverse())

export function toArray<T>(o: T | T[]): T[] {
  return Array.isArray(o) ? o : [o]
}

export function createAutoIncrementGenerator(prefix = '') {
  let id = 0

  return () => prefix + id++
}

export type Fn = (...opt: any[]) => any

export function toFixed(num: number, fractionDigits: number) {
  return +num.toFixed(fractionDigits)
}

export function getMapValue<T>(
  kv: Record<string | number, T>,
  key: string | number,
): T | undefined {
  return kv[key]
}

export function fnWrapper<T extends (...params: any[]) => any, R>(
  fn: T,
  wrapper: (param: Awaited<ReturnType<T>>) => R,
) {
  return async function (...params: Parameters<T>) {
    const r = await fn(...params)
    const result = await wrapper(r)

    return result
  }
}

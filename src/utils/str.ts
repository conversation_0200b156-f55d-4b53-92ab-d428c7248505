export interface SearchIndexOption {
  caseSensitive?: boolean
}

export function searchIndex(text?: string, searchText?: string, opt?: SearchIndexOption) {
  if (!searchText)
    return -1
  if (!text)
    return -1

  if (opt?.caseSensitive) {
    return text.indexOf(searchText)
  }

  return text.toLowerCase().indexOf(searchText.toLowerCase())
}

/**
 * 根据文本计算一个唯一的数字，然后对 length 取余
 *
 * @param text
 * @param length
 */
export function getFakeHashIndex(text: string, length: number) {
  let sum = 0

  for (let idx = 0; idx < text.length; idx++) {
    sum += text.charCodeAt(idx)
  }

  return sum % length
}

/**
 * 字符串换行
 * @param str
 * @returns
 */
export function formatWrapStr(str?: string) {
  if (!str)
    return ''
  const list = str.split(/[\n,]/g)
  return list.join('<br />')
}

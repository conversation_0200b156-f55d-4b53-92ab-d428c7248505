export interface CellElement {
  /**
   * 属性编码
   */
  code?: string
  /**
   * 属性ID
   */
  id?: string
  /**
   * 属性名称
   */
  name?: string
  /**
   * 质检点属性值
   */
  value?: string
  [property: string]: any
}

export function getCellByCode(cells: CellElement[] | undefined, code: string) {
  return cells?.find(n => n.code === code)
}

type ModelToJsonResult<T extends Record<string, string>> = { id: string, value: string } & {
  [key in T[keyof T]]: string
}

export interface ModelValue {
  /**
   * 质检点属性集合
   */
  cells?: CellElement[]
  /**
   * 质检点ID
   */
  id?: string
}

/**
 *
 * @param model
 * @param mapper modelKey => resultKey
 */
export function mapModelToJson<const T extends Record<string, string>>(
  model: ModelValue,
  /**
   * model code => record property name
   */
  mapper: T,
): ModelToJsonResult<T> {
  const result: Record<string, string | undefined> = {
    id: model.id,
    value: model.id,
  }

  for (const modelKey in mapper) {
    const resultKey = mapper[modelKey]
    result[resultKey] = getCellByCode(model.cells, modelKey)?.value
  }

  return result as any
}

/**
 * 微信SDK工具类测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { wechatSDK, isWechatBrowser } from '../wechat'

// Mock window对象
const mockWindow = {
  navigator: {
    userAgent: ''
  },
  document: {
    createElement: vi.fn(),
    head: {
      appendChild: vi.fn()
    }
  }
} as any

// Mock微信对象
const mockWx = {
  config: vi.fn(),
  ready: vi.fn(),
  error: vi.fn(),
  scanQRCode: vi.fn()
}

describe('微信SDK工具类', () => {
  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks()
    
    // 设置全局window对象
    global.window = mockWindow
    global.document = mockWindow.document
    
    // 重置SDK状态
    wechatSDK.reset()
  })

  describe('isWechatBrowser', () => {
    it('应该正确检测微信浏览器', () => {
      // 模拟微信浏览器
      mockWindow.navigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.0'
      
      expect(isWechatBrowser()).toBe(true)
    })

    it('应该正确检测非微信浏览器', () => {
      // 模拟普通浏览器
      mockWindow.navigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
      
      expect(isWechatBrowser()).toBe(false)
    })
  })

  describe('loadWxSDK', () => {
    it('应该能够加载微信SDK', async () => {
      const mockScript = {
        src: '',
        async: false,
        onload: null as any,
        onerror: null as any
      }
      
      mockWindow.document.createElement.mockReturnValue(mockScript)
      
      // 模拟脚本加载成功
      const loadPromise = wechatSDK.loadWxSDK()
      
      // 触发onload事件
      setTimeout(() => {
        global.window.wx = mockWx
        mockScript.onload?.()
      }, 0)
      
      await expect(loadPromise).resolves.toBeUndefined()
      expect(mockWindow.document.createElement).toHaveBeenCalledWith('script')
      expect(mockWindow.document.head.appendChild).toHaveBeenCalledWith(mockScript)
    })

    it('应该处理SDK加载失败', async () => {
      const mockScript = {
        src: '',
        async: false,
        onload: null as any,
        onerror: null as any
      }
      
      mockWindow.document.createElement.mockReturnValue(mockScript)
      
      // 模拟脚本加载失败
      const loadPromise = wechatSDK.loadWxSDK()
      
      // 触发onerror事件
      setTimeout(() => {
        mockScript.onerror?.()
      }, 0)
      
      await expect(loadPromise).rejects.toThrow('微信JS-SDK加载失败')
    })
  })

  describe('configWx', () => {
    beforeEach(() => {
      // 模拟微信SDK已加载
      global.window.wx = mockWx
    })

    it('应该能够配置微信SDK', async () => {
      const config = {
        debug: false,
        appId: 'test-app-id',
        timestamp: 1640995200,
        nonceStr: 'test-nonce',
        signature: 'test-signature',
        jsApiList: ['scanQRCode']
      }
      
      // 模拟配置成功
      const configPromise = wechatSDK.configWx(config)
      
      // 触发ready事件
      setTimeout(() => {
        const readyCallback = mockWx.ready.mock.calls[0][0]
        readyCallback()
      }, 0)
      
      await expect(configPromise).resolves.toBeUndefined()
      expect(mockWx.config).toHaveBeenCalledWith(config)
      expect(mockWx.ready).toHaveBeenCalled()
    })

    it('应该处理配置失败', async () => {
      const config = {
        debug: false,
        appId: 'test-app-id',
        timestamp: 1640995200,
        nonceStr: 'test-nonce',
        signature: 'test-signature',
        jsApiList: ['scanQRCode']
      }
      
      // 模拟配置失败
      const configPromise = wechatSDK.configWx(config)
      
      // 触发error事件
      setTimeout(() => {
        const errorCallback = mockWx.error.mock.calls[0][0]
        errorCallback(new Error('配置失败'))
      }, 0)
      
      await expect(configPromise).rejects.toThrow()
      expect(mockWx.config).toHaveBeenCalledWith(config)
      expect(mockWx.error).toHaveBeenCalled()
    })
  })

  describe('scanQRCode', () => {
    beforeEach(() => {
      // 模拟微信SDK已加载并配置成功
      global.window.wx = mockWx
      wechatSDK['status'] = 'ready' as any
    })

    it('应该能够调用扫码功能', async () => {
      const mockResult = { resultStr: 'test-qr-code-result' }
      
      // 模拟扫码成功
      mockWx.scanQRCode.mockImplementation((config: any) => {
        setTimeout(() => {
          config.success(mockResult)
        }, 0)
      })
      
      const result = await wechatSDK.scanQRCode({
        needResult: 1,
        scanType: ['qrCode']
      })
      
      expect(result).toEqual(mockResult)
      expect(mockWx.scanQRCode).toHaveBeenCalledWith({
        needResult: 1,
        scanType: ['qrCode'],
        success: expect.any(Function),
        fail: expect.any(Function),
        complete: undefined
      })
    })

    it('应该处理扫码失败', async () => {
      const mockError = new Error('扫码失败')
      
      // 模拟扫码失败
      mockWx.scanQRCode.mockImplementation((config: any) => {
        setTimeout(() => {
          config.fail(mockError)
        }, 0)
      })
      
      await expect(wechatSDK.scanQRCode()).rejects.toThrow()
      expect(mockWx.scanQRCode).toHaveBeenCalled()
    })
  })

  describe('getStatus', () => {
    it('应该返回当前状态', () => {
      expect(wechatSDK.getStatus()).toBe('not_loaded')
    })
  })

  describe('reset', () => {
    it('应该重置SDK状态', () => {
      wechatSDK.reset()
      expect(wechatSDK.getStatus()).toBe('not_loaded')
    })
  })
})

// @ts-expect-error
import ClientMonitor from '@tce/online-monitoring'
import { joinURL } from 'ufo'
import { ONLINE_MONITOR_APPID, ONLINE_MONITOR_SERVICE, ONLINE_MONITOR_URL } from '@/env'
import { APP_CONFIG, MONITOR_CONFIG } from '@/config'
import { router } from '@/router'
import { currentUserInfo } from '@/store/sysUser'

/**
 * 注册监控服务
 */
export function registerOnlineMonitor() {
  const conf = getMonitorConfig()

  if (!conf.appId || !conf.service) {
    console.debug('Monitor 配置缺失, 取消注册')
    return
  }

  ClientMonitor.register({
    appID: conf.appId, // 应用ID
    collector: conf.url, // 监控平台地址
    service: conf.service, // 业务系统名，必须英文，如果是多租户，采用 'service-name---租户ID'
    pagePath: window.location.hash, // 路由地址，注意：哈希路由用window.location.hash
    serviceVersion: APP_CONFIG.version, // 业务系统版本号
    feedback: false, // boolean/json 默认打开意见反馈弹窗，设为false可关闭
    noTraceOrigins: ['https://onlinemonitor-server.geega.com'], // 白名单
  })
}

export function monitorRouteChange(toPath: string) {
  const conf = getMonitorConfig()
  if (!conf.appId) return

  const user = currentUserInfo.value || {}

  ClientMonitor.setPerformance({
    userInfo: {
      ...user,
      documentTitle: document.title, // 页面中文名 title，根据实际项目配置 1.0.4及以上版本支持
    },
    pagePath: toPath, // 路由地址，获取路由的相对路径，哈希路由要带#号
  })
}

function getMonitorConfig() {
  if (!MONITOR_CONFIG.enable) {
    return {}
  }

  let url = ONLINE_MONITOR_URL || ''

  url = url.startsWith('http') ? url : joinURL(location.origin, url)

  const monitorRuntimeConf = APP_CONFIG.runtime.monitor || {}

  return {
    service: monitorRuntimeConf.service || ONLINE_MONITOR_SERVICE,
    appId: monitorRuntimeConf.appID || ONLINE_MONITOR_APPID,
    url,
  }
}

export function reportError(err: Error) {
  const conf = getMonitorConfig()
  if (!conf.appId) return

  ClientMonitor.reportFrameErrors(
    {
      pagePath: router.currentRoute.value.fullPath, // 路由地址，注意：哈希路由用window.location.hash，也可以用route.fullPath
    },
    err, // 此处为 Error 对象，如：new Error('error.msg')
  )
}

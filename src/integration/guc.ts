import {
  V1OpenApiUserLoginGucPost,
  V1OpenApiUserLoginPublicKey,
} from '@/api/api.req'
import { useCurrentUser } from '@/store/sysUser'
import { useLocalStorage } from '@vueuse/core'
import JSEncrypt from 'jsencrypt'
import { showConfirmDialog } from 'vant'

interface GucState {
  token: string
  refreshToken: string
}

const defaultState: GucState = {
  token: '',
  refreshToken: '',
}

const gucState = useLocalStorage<GucState>(`guc-store`, defaultState)

export const gucStore = {
  getToken() {
    return gucState.value.token
  },
  async login(opt: LoginOptions) {
    const result = await loginApi(opt)

    gucState.value.token = result.token!
    gucState.value.refreshToken = result.refreshToken!
    return true
  },

  async logout() {
    gucState.value.token = ''
    gucState.value.refreshToken = ''

    useCurrentUser().clear()
  },
  async logoutConfirm(msg?: string) {
    await showConfirmDialog({
      title: msg || '确认退出登录？',
      className: 'warn-dialog',
    })
    gucStore.logout()
  },
  /**
   * Not implement
   *
   * @param code
   * @returns
   */
  hasPermission(code?: string) {
    if (!code) return true

    return false
  },
}

export interface LoginOptions {
  type: LoginType
  username: string
  password: string
}

/**
 * 登录类型 1普通账户 2验证码登录 3域账号登录 4刷卡登录 5企微授权登录
 *
 * {@link V1OpenAPIUserLoginGucPostRequestBody.loginType}
 */
export enum LoginType {
  /**
   * 账号/密码
   */
  Password = 1,
  /**
   * 域账号
   */
  Domain = 3,
}

async function loginApi(opt: LoginOptions) {
  const loginPublicKey = await V1OpenApiUserLoginPublicKey({ username: opt.username })

  const encrypt = new JSEncrypt()
  encrypt.setPublicKey(loginPublicKey)
  const encryptedPassword = encrypt.encrypt(opt.password)

  if (!encryptedPassword) {
    throw new Error('Encrypt password failed!')
  }

  const loginInfo = await V1OpenApiUserLoginGucPost({
    username: opt.username,
    password: encryptedPassword,
    loginType: opt.type,
  })

  return loginInfo
}

<script lang="ts" setup>
import { debounce } from 'lodash-es'
import Footer from '@/components/layOut/footer.vue'
import TestPaperItem from '@/pages/components/test-paper-item.vue'
import { V1MobileHomePagePost } from '@/api/api.req'
import { useCurrentUser } from '@/store/sysUser'
import EmptyData from '@/components/EmptyData.vue'
import type { V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult } from '@/api/api.model'
import { PaperTypeEnum, ExamStatusEnum } from '@/enums/exam'

const router = useRouter()

const sysUserIns = useCurrentUser()

const vanListProps = reactive({
  loading: true,
  finished: false,
  data: [] as V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult[],
})

const pullRefreshIng = ref<boolean>(false)

const PAGE_SIZE = 20

const currentPage = ref<number>(0)

async function loadData(isPullRefresh?: boolean) {
  const userId = sysUserIns.sysUserGet?.id
  if (!userId) {
    vanListProps.loading = false
    return
  }
  vanListProps.loading = true

  const params = {
    pageSize: PAGE_SIZE,
    currentPage: currentPage.value + 1,
    data: {
      userId,
      type: PaperTypeEnum.SKILL_ASSESSMENT,
      status: ExamStatusEnum.COMPLETED,
    },
  }

  const resp = await V1MobileHomePagePost(params)
  pullRefreshIng.value = false

  currentPage.value = params.currentPage

  const records = resp.records || []

  vanListProps.data = isPullRefresh ? records : [...vanListProps.data, ...records]

  vanListProps.finished = currentPage.value >= resp.totalPages!

  nextTick(() => (vanListProps.loading = false))
}

function jumpViewAnswersExample(id: string) {
  router.push({
    path: '/on-site-assessment/view-answers-example',
    query: {
      id: id,
    },
  })
}

function clear() {
  vanListProps.finished = false
  vanListProps.loading = true
  currentPage.value = 0
}

function refresh() {
  clear()
  loadData(true)
}

const scrollLoad = debounce(() => {
  loadData()
}, 200)

onMounted(loadData)

const isShowEmpty = computed(() => !vanListProps.data?.length && !vanListProps.loading)
</script>

<template>
  <VanNavBar title="考试系统-现场考核" />
  <div class="mt-10px box-border h-[calc(100vh-110px)] overflow-y-auto px-10px">
    <VanPullRefresh v-model="pullRefreshIng" success-text="刷新成功!" @refresh="refresh">
      <VanList
        :loading="vanListProps.loading"
        :finished="vanListProps.finished"
        :finished-text="isShowEmpty ? '' : '没有更多了'"
        :offset="20"
        :immediate-check="false"
        @load="scrollLoad"
      >
        <div v-for="item in vanListProps.data" :key="item.id" class="pb-10px">
          <TestPaperItem
            v-bind="{ testPaper: item, showScore: false }"
            @click-detail="jumpViewAnswersExample(item.id!)"
          />
        </div>
      </VanList>
      <EmptyData v-if="isShowEmpty" />
    </VanPullRefresh>
  </div>
  <Footer />
</template>

<style lang="less" scoped></style>

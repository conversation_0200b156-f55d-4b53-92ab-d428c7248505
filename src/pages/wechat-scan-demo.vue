<script setup lang="ts">
import { ref } from 'vue'
import { showToast } from 'vant'
import QrScanner from '@/components/QrScanner/index.vue'

// 页面状态
const scanResults = ref<Array<{ time: string, result: string, parsed?: any }>>([])

// 处理扫码成功
function handleScanSuccess(result: string, parsedResult?: any) {
  console.log('扫码成功:', result, parsedResult)
  
  // 记录扫码结果
  scanResults.value.unshift({
    time: new Date().toLocaleString(),
    result,
    parsed: parsedResult
  })
  
  showToast('扫码成功')
}

// 处理扫码失败
function handleScanError(error: Error) {
  console.error('扫码失败:', error)
  showToast(`扫码失败: ${error.message}`)
}

// 清空扫码记录
function clearResults() {
  scanResults.value = []
  showToast('已清空记录')
}
</script>

<template>
  <div class="wechat-scan-demo">
    <!-- 导航栏 -->
    <van-nav-bar title="微信扫码演示" left-arrow @click-left="$router.back()" />
    
    <!-- 主要内容 -->
    <div class="p-4">
      <!-- 扫码区域 -->
      <div class="scan-section mb-6">
        <div class="mb-4">
          <h3 class="text-lg font-bold mb-2">扫码功能</h3>
          <p class="text-sm text-gray-6 mb-4">
            在微信环境中会自动使用微信扫码API，在其他环境中会使用模拟扫码（开发测试用）
          </p>
        </div>
        
        <!-- 扫码按钮组 -->
        <div class="flex flex-wrap gap-3">
          <QrScanner
            button-text="标准扫码"
            button-type="primary"
            :on-success="handleScanSuccess"
            :on-error="handleScanError"
          />
          
          <QrScanner
            button-text="成功扫码"
            button-type="success"
            button-size="small"
            :on-success="handleScanSuccess"
            :on-error="handleScanError"
          />
          
          <QrScanner
            button-text=""
            button-type="default"
            button-size="mini"
            button-class="!px-2"
            :on-success="handleScanSuccess"
            :on-error="handleScanError"
          />
        </div>
      </div>
      
      <!-- 扫码记录 -->
      <div class="results-section">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-bold">扫码记录</h3>
          <van-button
            v-if="scanResults.length > 0"
            type="danger"
            size="small"
            plain
            @click="clearResults"
          >
            清空记录
          </van-button>
        </div>
        
        <!-- 记录列表 -->
        <div v-if="scanResults.length > 0" class="space-y-3">
          <div
            v-for="(item, index) in scanResults"
            :key="index"
            class="result-item bg-gray-1 rounded-lg p-3"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-6">{{ item.time }}</span>
              <van-tag type="primary" size="small">
                {{ item.parsed?.type || 'text' }}
              </van-tag>
            </div>
            
            <div class="result-content">
              <div class="mb-2">
                <div class="text-sm text-gray-7 mb-1">原始内容：</div>
                <div class="text-sm break-all bg-white rounded p-2">
                  {{ item.result }}
                </div>
              </div>
              
              <div v-if="item.parsed" class="parsed-info">
                <div class="text-sm text-gray-7 mb-1">解析结果：</div>
                <div class="text-sm bg-white rounded p-2">
                  <!-- 工位信息 -->
                  <template v-if="item.parsed.type === 'workstation_info'">
                    <div class="space-y-1">
                      <div v-if="item.parsed.workstationName">
                        <span class="font-medium">工位：</span>{{ item.parsed.workstationName }}
                      </div>
                      <div v-if="item.parsed.levelName">
                        <span class="font-medium">层级：</span>{{ item.parsed.levelName }}
                      </div>
                      <div v-if="item.parsed.userName">
                        <span class="font-medium">操作员：</span>{{ item.parsed.userName }}
                      </div>
                      <div v-if="item.parsed.projectName">
                        <span class="font-medium">项目：</span>{{ item.parsed.projectName }}
                      </div>
                    </div>
                  </template>
                  
                  <!-- 登录信息 -->
                  <template v-else-if="item.parsed.type === 'login'">
                    <div class="space-y-1">
                      <div v-if="item.parsed.deviceIp">
                        <span class="font-medium">设备IP：</span>{{ item.parsed.deviceIp }}
                      </div>
                      <div v-if="item.parsed.token">
                        <span class="font-medium">Token：</span>{{ item.parsed.token.substring(0, 16) }}...
                      </div>
                    </div>
                  </template>
                  
                  <!-- URL信息 -->
                  <template v-else-if="item.parsed.type === 'url'">
                    <div>
                      <span class="font-medium">链接：</span>
                      <a :href="item.parsed.url" target="_blank" class="text-blue-6 underline">
                        {{ item.parsed.url }}
                      </a>
                    </div>
                  </template>
                  
                  <!-- 其他类型 -->
                  <template v-else>
                    <pre class="whitespace-pre-wrap text-xs">{{ JSON.stringify(item.parsed, null, 2) }}</pre>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-else class="empty-state text-center py-8">
          <van-empty description="暂无扫码记录" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.wechat-scan-demo {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.scan-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.results-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.result-item {
  border: 1px solid #ebedf0;
  
  .result-content {
    font-size: 14px;
    
    .parsed-info {
      border-top: 1px solid #ebedf0;
      padding-top: 8px;
      margin-top: 8px;
    }
  }
}
</style>

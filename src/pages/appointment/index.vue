<script lang="ts" setup>
import Footer from '@/components/layOut/footer.vue'
import { useLoading } from '@/hooks/useLoading'
import { useAsyncData } from '@/hooks/useAsyncData'
import { V1ManageSysUnitTerminalList, V1ManageTrainBookingsListPost } from '@/api/api.req'
import type { StationItem } from '@/components/StationsStatus.vue'
import { StationStatus } from '@/enums/station'
import dayjs from 'dayjs'
import { getOption } from '@/utils'
import { AppointmentStatusOptions } from '@/enums/appointment'

const router = useRouter()

const state = reactive({
  selectedStationId: undefined as undefined | string,
  /**
   * eg. 2024-01-01
   */
  selectedDatetime: undefined as undefined | string,
})

const stationsList = useAsyncData(async () => {
  const resp = await V1ManageSysUnitTerminalList()

  const items = resp.map(
    (item) =>
      ({
        ...item,
        id: item.unitId!,
        label: item.unitName!,
        indicator: item.status === StationStatus.IN_USE,
      }) as StationItem,
  )

  if (!items.find((i) => i.id === state.selectedStationId)) {
    state.selectedStationId = items.at(0)?.id
  }

  return items
}, [])

const appointmentList = useAsyncData(async () => {
  if (!state.selectedDatetime) {
    return []
  }
  if (!state.selectedStationId) {
    return []
  }

  const selectedDay = dayjs(state.selectedDatetime, 'YYYY-MM-DD').startOf('d')

  const resp = await V1ManageTrainBookingsListPost({
    startTime: selectedDay.format('YYYY-MM-DD HH:mm:ss') as any,
    endTime: selectedDay.endOf('d').format('YYYY-MM-DD HH:mm:ss') as any,
    unitId: state.selectedStationId,
  })

  const configs = resp.map((item) => ({
    ...item,
    start: dayjs(item.startTime).format('HH:mm'),
    end: dayjs(item.endTime).format('HH:mm'),
    label: item.unitName!,
    user: item.userName!,
    color: getOption(AppointmentStatusOptions, item.status)?.color!,
  }))

  // sort by start time
  configs.sort((a, b) => {
    return dayjs(a.startTime).unix() - dayjs(b.startTime).unix()
  })

  return configs
}, [])

watch(
  () => [state.selectedStationId, state.selectedDatetime],
  () => {
    appointmentList.load()
  },
)

const fetchData = useLoading(_fetchData)
fetchData()

function _fetchData() {
  stationsList.load()
  appointmentList.load()
}

function onAddBtnClick() {
  router.push('/appointment/add')
}
</script>

<template>
  <VanNavBar title="预约" />
  <div class="box-border h-[calc(100vh-100px)] overflow-y-auto px-10px pt-10px">
    <VanPullRefresh
      :model-value="fetchData.isLoading"
      success-text="刷新成功!"
      @refresh="fetchData"
    >
      <div class="flex mb-2">
        <div class="flex-1 title">工作台状态</div>
        <div class="status">
          <StatusIndicator> 使用中 </StatusIndicator>
        </div>
      </div>
      <StationsStatus :stations="stationsList.data.value" v-model="state.selectedStationId" />
      <CardBox class="mt-4">
        <div class="title mb-4">工作台预约</div>
        <div class="appointment">
          <WeeklyTimeline
            v-model="state.selectedDatetime"
            :timelines="appointmentList.data.value"
          />
        </div>
      </CardBox>
    </VanPullRefresh>

    <VanFloatingBubble
      class="floating-add-btn"
      icon="plus"
      axis="xy"
      :gap="{ x: 10, y: 60 }"
      @click="onAddBtnClick"
    />
  </div>
  <Footer />
</template>

<style lang="less" scoped>
.title {
  font-weight: bold;
}
</style>

<style lang="less">
.van-floating-bubble.floating-add-btn {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  background: var(--Brand-Brand7-Normal, #00996b);
  box-shadow: 4px 4px 4px 0px rgba(0, 0, 0, 0.12);

  .van-icon {
    font-size: 22px;
  }
}
</style>

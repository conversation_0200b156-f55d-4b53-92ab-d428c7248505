<script lang="ts" setup>
import type { V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult } from '@/api/api.model'

type TestPaperInfo = V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult

export interface TestPaperItemProps {
  testPaper: TestPaperInfo
  showScore?: boolean
  footerLabel?: string
}

const props = withDefaults(defineProps<TestPaperItemProps>(), {
  showScore: false,
})

const emit = defineEmits(['clickDetail'])

function clickDetail(item: TestPaperInfo) {
  emit('clickDetail', item)
}

const tags = computed(() => {
  const _questionPropertyList = props.testPaper.questionPropertyList || []
  return _questionPropertyList.map(o => o.columnValue).filter(o => Boolean(o))
})
</script>

<template>
  <div class="paper-item rounded-8px bg-[#fff] px-16px" @click="clickDetail(testPaper)">
    <div class="flex">
      <div
        class="mr-6px mt-12px h-22px w-22px flex items-center justify-center rounded-50% bg-[#04D696]"
      >
        <div class="h-16px w-16px">
          <img class="h-full w-full align-top" src="@/assets/icons/svg/test-paper-icon.svg">
        </div>
      </div>
      <div class="flex-1 break-all pb-14px pt-10px font-size-17px color-[#666] font-500">
        <VanTextEllipsis :content="testPaper.paperName || '-'">
          <template #action="{ expanded }">
            <VanIcon v-if="!expanded" name="arrow-down" />
            <VanIcon v-else name="arrow-up" />
          </template>
        </VanTextEllipsis>
      </div>
      <div v-if="showScore" class="mt-15px w-50px flex justify-end font-size-14px color-[#999]">
        {{ testPaper.score }}分
      </div>
    </div>
    <div v-if="tags.length" class="flex gap-12px pb-8px">
      <VanTag
        v-for="(tag, idx) in tags"
        :key="`${idx}_${tag}`"
        color="#E7E7E7"
        text-color="#333"
        size="large"
      >
        {{ tag }}
      </VanTag>
    </div>
    <div
      class="h-40px flex border-1px border-color-[#f4f6f8] border-t-solid font-size-14px color-[#999] lh-40px"
    >
      <div class="flex-1">
        <span class="">{{ testPaper.limitBegTime || '-' }}</span>
        -
        <span>{{ testPaper.limitEndTime || '-' }}</span>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.paper-item {
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
}
</style>

<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import {
  type QuestionType,
  type TestPaperOtherProps,
  type TestPaperQuestionsGroupCell,
  getQuestionDifficultyText,
  getQuestionModeText,
} from './const'
import ShortAnswerQuestion from './question-item/short-answer-question.vue'
import MultipleChoiceQuestion from '@/pages/components/question-item/multiple-choice-question.vue'
import SingleChoiceQuestion from '@/pages/components/question-item/single-choice-question.vue'
import JudgeQuestion from '@/pages/components/question-item/judge-question.vue'
import FillInTheBlanksQuestion from '@/pages/components/question-item/fill-in-the-blanks-question.vue'
import { QUESTION_TYPE } from '@/config'
import type { ResultQuestionTypeList } from '@/api/api.model'
import EmptyData from '@/components/EmptyData.vue'

export interface TestPaperQuestionRenderProps extends TestPaperOtherProps {
  testPaperValue: TestPaperQuestionsGroupCell[]
  questionTypeList: ResultQuestionTypeList[]
}

const props = defineProps<TestPaperQuestionRenderProps>()

const emit = defineEmits(['update:testPaperValue'])

function resolveComponent(qType: QuestionType) {
  const componentsMapper = {
    /**
     * 填空题组件
     */
    [QUESTION_TYPE.FILL_IN_THE_BLANK]: FillInTheBlanksQuestion,
    /**
     * 单选题组件
     */
    [QUESTION_TYPE.SINGLE_CHOICE]: SingleChoiceQuestion,
    /**
     * 判断题组件
     */
    [QUESTION_TYPE.JUDGMENT]: JudgeQuestion,

    [QUESTION_TYPE.MULTIPLE_CHOICE]: MultipleChoiceQuestion,
    [QUESTION_TYPE.SHORT_ANSWER]: ShortAnswerQuestion,
  }
  return componentsMapper[qType]
}

function calcRuleScore(item: TestPaperQuestionsGroupCell) {
  return props.questionTypeList.find(
    i => i.questionType === item.qType && i.difficultDegree?.toString() === item.qDifficulty,
  )?.singleScore
}

const vTestPaperValue = useVModel(props, 'testPaperValue', emit)
</script>

<template>
  <div v-for="groupCell in vTestPaperValue || []" :key="groupCell.qType">
    <div class="pb-8px pt-12px font-500">
      {{ getQuestionModeText(groupCell.qType as QuestionType) }} -
      {{ getQuestionDifficultyText(groupCell.qDifficulty!) }}（每题
      {{ calcRuleScore(groupCell) }} 分）
    </div>
    <div class="overflow-hidden rounded-8px">
      <component
        :is="resolveComponent(item.questionType as QuestionType)"
        v-for="(item, idx) in groupCell.questions"
        :key="item.id"
        v-model:value="item.userAnswer"
        :question="item"
        :disbaled="disbaled"
        :show-example="showExample"
        :index="idx + 1"
        :q-rule-score="calcRuleScore(groupCell)!"
      />
    </div>
  </div>
  <div v-if="!vTestPaperValue?.length" class="pt-50px">
    <EmptyData tip="该试卷无试题。" />
  </div>
</template>

<style lang="less" scoped></style>

<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import { type QuestionItem, type TestPaperOtherProps, alphabetCode } from '../const'
import QuestionCommonRenderPart from '@/pages/components/question-item/common/question-common-render-part.vue'

const props = defineProps<{
  value: number
  index: number
  question: QuestionItem
  qRuleScore: number
} & TestPaperOtherProps>()

const emit = defineEmits(['update:value'])

const _value = useVModel(props, 'value', emit)

const remarkScore = computed(() => {
  const calcScore = props.question?.calcScore
  if (props.disbaled && calcScore !== undefined) {
    return calcScore > 0 ? `<span style="color: #00996B">(+${calcScore})</span>` : ''
  }
  return ''
})

type QOpts = QuestionItem['questionOption']

function resolveSysAnswer(qOpts: QOpts = []) {
  const list = qOpts.reduce((iter, item, index) => {
    if (item.correctAnswer) {
      const opt = `${alphabetCode[index]}`
      return [...iter, opt]
    }
    return [...iter]
  }, [] as string [])
  return list.join('、')
}

const commonRenderProps = computed(() => {
  return {
    qName: `${props.index}、${props.question.questionStem} ${remarkScore.value}`,
    qCorrectAnswer: resolveSysAnswer(props.question.questionOption),
    qAnswerAnalysis: props.question.answerAnalysis,
    showExample: props.showExample,
  }
})
</script>

<template>
  <QuestionCommonRenderPart v-bind="commonRenderProps">
    <div class="opts-list">
      <VanRadioGroup v-model="_value" :disabled="disbaled">
        <VanRadio v-for="(item, idx) in question.questionOption || []" :key="item.orderNum" :name="item.orderNum" :class="{ 'success-answer': _value === item.orderNum && question.calcScore !== undefined && question.calcScore > 0, 'error-answer': _value === item.orderNum && question.calcScore !== undefined && question.calcScore === 0 }">
          <div class="opt-text opt-text html-wrap flex color-[#333]" v-html="`${alphabetCode[idx]}、${item.optionDesc}`" />
        </VanRadio>
      </VanRadioGroup>
    </div>
  </QuestionCommonRenderPart>
</template>

<style lang="less" scoped>
@import './common/index.less';
</style>

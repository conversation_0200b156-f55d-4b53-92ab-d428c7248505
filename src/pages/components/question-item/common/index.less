
@success-mark-color: #3EC05C;

@error-mark-color: #D9001B;


.opts-list {
  :deep(.van-radio) {
    padding-top: 15px;
    &:first-child{
      padding-top: 5px;
    }
    &__label {
      color: #999;
      margin-left: 16px;
      font-size: 15px;
    }
  }
  :deep(.van-checkbox) {
    padding-top: 15px;
    &:first-child{
      padding-top: 5px;
    }
    &__label {
      color: #999;
      margin-left: 16px;
      font-size: 15px;
    }
  }
}

:deep(.van-field) {
  border-radius: 4px;
  padding-top: 4px;
  padding-bottom: 4px;
  border: 1px solid #f5f6f7;
}

.success-answer{
  color: @success-mark-color !important;
  .opt-text{
    color: @success-mark-color !important;
  }
}

.error-answer{
  color: @error-mark-color !important;
  .opt-text{
    color: @error-mark-color !important;
  }
}

:deep(.van-radio__icon--disabled) {
  .van-icon {
    background-color: #fff !important;
  }
}

.opt-icon-bg(@color) {
  :deep(.van-radio__icon--disabled) {
    &.van-radio__icon--checked {
      .van-icon {
        background-color: @color !important;
        border-color: transparent;
        &::before {
          color: #fff;
        }
      }
    }
  }


  :deep(.van-checkbox__icon--disabled) {
    &.van-checkbox__icon--checked {
      .van-icon {
        background-color: @color !important;
        border-color: transparent;
        &::before {
          color: #fff;
        }
      }
    }
  }
}

.error-answer{
  .opt-icon-bg(@error-mark-color)
}

.success-answer{
  .opt-icon-bg(@success-mark-color)
}

.html-wrap {
  word-break: break-all;
  :deep(>p, >div){
    display: inline;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;
  }
  :deep(img){
    vertical-align: middle !important;
  }
}

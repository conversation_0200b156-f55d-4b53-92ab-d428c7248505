<script lang="ts" setup>
defineProps<{
  qName?: string
  qCorrectAnswer?: string
  qAnswerAnalysis?: string
  showExample?: boolean
}>()
</script>

<template>
  <div class="bg-[#fff] px-8px py-15px">
    <div class="html-wrap break-all pb-10px text-wrap font-size-15px font-400" v-html="qName" />
    <slot />
    <div v-if="showExample" class="font-size-15px color-[#3EC05C]">
      <div class="break-all pt-20px">
        <span class="inline-block pr-4px">参考答案:</span>
        <span class="html-wrap" v-html="qCorrectAnswer || '-'" />
      </div>
      <div class="break-all pt-10px color-[#999]">
        <span class="inline-block pr-4px">答案解析:</span>
        <span class="html-wrap" v-html="qAnswerAnalysis || '-'" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import './index.less';
</style>

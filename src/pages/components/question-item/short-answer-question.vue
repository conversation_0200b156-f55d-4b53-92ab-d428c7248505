<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import type { QuestionItem, TestPaperOtherProps } from '../const'
import QuestionCommonRenderPart from '@/pages/components/question-item/common/question-common-render-part.vue'

const props = defineProps<{
  value: string[]
  index: number
  question: QuestionItem
  qRuleScore: number
} & TestPaperOtherProps>()

const emit = defineEmits(['update:value'])

const _value = useVModel(props, 'value', emit)

const remarkScore = computed(() => {
  const calcScore = props.question?.calcScore
  if (props.disbaled && calcScore !== undefined) {
    return calcScore > 0 ? `<span style="color: #00996B">(+${calcScore})</span>` : ''
  }
  return ''
})

type QOpts = QuestionItem['questionOption']

function resolveSysAnswer(qOpts: QOpts = []) {
  const list = qOpts.reduce((iter, item, index) => {
    const _idx = qOpts.length > 1 ? `${index + 1}、` : ''
    const opt = `${_idx}${item.optionDesc}`
    return [...iter, opt]
  }, [] as string [])
  return list.join(';  ')
}

function eqStr(v1?: string, v2?: string) {
  if (v1 === v2) {
    return true
  }
  else {
    if (typeof v1 === 'string' && typeof v2 === 'string') {
      return v1.trim() === v2.trim()
    }
    else {
      return false
    }
  }
}

const commonRenderProps = computed(() => {
  return {
    qName: `${props.index}、${props.question.questionStem} ${remarkScore.value}`,
    qCorrectAnswer: resolveSysAnswer(props.question.questionOption),
    qAnswerAnalysis: props.question.answerAnalysis,
    showExample: props.showExample,
  }
})
</script>

<template>
  <QuestionCommonRenderPart v-bind="commonRenderProps">
    <div>
      <div v-for="(v, idx) in _value" :key="idx" class="flex items-center pt-6px">
        <div class="w-30px" :class="{ invisible: idx !== 0 }">
          答：
        </div>
        <div v-if="_value.length > 1" class="w-20px" :class="{ 'w-30px': disbaled }">
          {{ idx + 1 }}<span v-if="disbaled">、</span>
        </div>
        <div class="flex-1">
          <van-field
            v-if="!disbaled"
            v-model="_value[idx]"
            autocomplete="off"
            clearable
            placeholder="请输入"
          />
          <div v-else class="break-all font-size-14px" :class="[eqStr(v, question.questionOption?.[idx]?.optionDesc) ? 'success-answer' : 'error-answer']">
            {{ v }}
          </div>
        </div>
      </div>
    </div>
  </QuestionCommonRenderPart>
</template>

<style lang="less" scoped>
@import './common/index.less';
</style>

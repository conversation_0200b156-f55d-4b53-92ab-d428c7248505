import { groupBy } from 'lodash-es'
import {
  QUESTION_DIFFICULT_DEGREE,
  QUESTION_TYPE,
  QuestionDifficultOptions,
  TEST_PAPER_MODE,
} from '@/config'
import type { V1ManageExamPaperQuestionListPaperIDGetResponseResult } from '@/api/api.model'

export type QuestionItem = V1ManageExamPaperQuestionListPaperIDGetResponseResult & {
  userAnswer: any
  calcScore?: number
}

type TestPaperMode = `${TEST_PAPER_MODE}`

export interface TestPaperOtherProps {
  disbaled?: boolean
  showExample?: boolean
}

export type QuestionType = `${QUESTION_TYPE}`

export function testPaperQuestionsRenderOtherProps(mode: TestPaperMode): TestPaperOtherProps {
  switch (mode) {
    case TEST_PAPER_MODE.ONLINE_WIRTE:
      return {
        disbaled: false,
        showExample: false,
      }
      break
    case TEST_PAPER_MODE.VIEW_USER_ANSWERS_AND_EXAMPLE:
      return {
        disbaled: true,
        showExample: true,
      }
      break

    case TEST_PAPER_MODE.VIEW_EXAMPLE:
      return {
        disbaled: true,
        showExample: true,
      }
      break
    default: {
      return {}
    }
  }
}

export function getQuestionModeText(type: QuestionType): string {
  const RecordMap = {
    [QUESTION_TYPE.FILL_IN_THE_BLANK]: '填空题',
    [QUESTION_TYPE.SINGLE_CHOICE]: '选择题',
    [QUESTION_TYPE.JUDGMENT]: '判断题',
    [QUESTION_TYPE.MULTIPLE_CHOICE]: '多选题',
    [QUESTION_TYPE.SHORT_ANSWER]: '简答题',
  }
  return RecordMap[type]
}

export function getQuestionDifficultyText(difficulty: string): string {
  return QuestionDifficultOptions.find((n) => n.value === difficulty)?.label || '未知难度'
}

export interface Opt {
  name: string
  code: string
}

export const alphabetCode = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
]

export function verifyIsFillBlank(qType: string) {
  return qType === QUESTION_TYPE.FILL_IN_THE_BLANK
}

export function verifyIsMultip(qType: string) {
  return qType === QUESTION_TYPE.MULTIPLE_CHOICE
}

export function isBlank(v: any) {
  return v === undefined || v === null || (typeof v === 'string' && !v.replaceAll(' ', ''))
}

export interface TestPaperQuestionsGroupCell {
  qType?: string
  qDifficulty?: string
  questions: QuestionItem[]
}

const ENCRYPT_CODE = '_fg'

const ENCRYPT_MAPPER_KEY = 'params'

export function encryptStr(value: string) {
  return `${ENCRYPT_CODE}${window.btoa(JSON.stringify({ [ENCRYPT_MAPPER_KEY]: value }))}`
}

export function decryptStr(value: string) {
  const _value = value.replace(ENCRYPT_CODE, '')
  const encryptValue = window.atob(_value)
  return JSON.parse(encryptValue || '{}')[ENCRYPT_MAPPER_KEY]
}

interface IGroupedQuestions<T extends QuestionItem> {
  orderType: string
  questions: T[]
}

export function groupQuestions<T extends QuestionItem>(
  questions: T[] = [],
): IGroupedQuestions<T>[] {
  const _questions = groupBy(
    questions || [],
    (item) => `${item.questionType}-${item.difficultDegree}`,
  )

  const result: IGroupedQuestions<T>[] = []

  const difficultyOrder = [
    QUESTION_DIFFICULT_DEGREE.EASY,
    QUESTION_DIFFICULT_DEGREE.NORMAL,
    QUESTION_DIFFICULT_DEGREE.HARD,
  ]

  const groupOrder = [
    QUESTION_TYPE.FILL_IN_THE_BLANK,
    QUESTION_TYPE.SINGLE_CHOICE,
    QUESTION_TYPE.JUDGMENT,
    QUESTION_TYPE.MULTIPLE_CHOICE,
    QUESTION_TYPE.SHORT_ANSWER,
  ].flatMap((t) => difficultyOrder.map((d) => `${t}-${d}`))

  for (const orderType of groupOrder) {
    const groupedQuestions = _questions[orderType]
    if (!groupedQuestions?.length) {
      continue
    }

    result.push({
      orderType,
      questions: groupedQuestions,
    })
  }

  return result
}

export function splitGroups(qs: QuestionItem[]): TestPaperQuestionsGroupCell[] {
  const grouped = groupQuestions(qs)

  return grouped.map((g) => {
    const [questionType, difficulty] = g.orderType.split('-')
    return {
      qType: questionType,
      qDifficulty: difficulty,
      questions: g.questions,
    }
  })
}

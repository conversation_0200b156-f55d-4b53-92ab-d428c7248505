<script lang="ts" setup>
import { watchImmediate } from '@vueuse/core'
import { baseConfigs } from './baseConfig'
import { domainConfig } from '@/integration/domain'

const emit = defineEmits(['changed'])

const basePickerOptions = baseConfigs.map((item) => ({
  ...item,
  text: item.name,
  value: item.host,
}))

const state = reactive({
  visible: false,
  currentBaseName: '',
  currentBaseHost: '',
  selected: [''],
})

function onConfirm(item: any) {
  state.visible = false

  const option = item.selectedOptions[0]

  state.currentBaseHost = option.value
  state.currentBaseName = option.text
}

function showBasePicker() {
  state.visible = true
}

function hideBasePicker() {
  state.visible = false
}

onMounted(() => {
  // init
  state.currentBaseName = domainConfig.value.name
  state.currentBaseHost = domainConfig.value.host
  state.selected = [state.currentBaseHost]
})

watchImmediate(
  () => state.currentBaseHost,
  () => {
    if (!state.currentBaseHost) {
      return
    }

    const baseInfo = {
      name: state.currentBaseName,
      host: state.currentBaseHost,
    }

    domainConfig.value.host = baseInfo.host
    domainConfig.value.name = baseInfo.name

    emit('changed', baseInfo)
  },
)
</script>

<template>
  <div>
    <div class="select-base" @click.stop="showBasePicker">
      <div class="select-base-value" :class="{ 'no-select-base': !state.currentBaseHost }">
        {{ state.currentBaseName || '请选择基地' }}
      </div>
      <div class="select-base-icon">
        <img src="../../../assets/img/switch-icon.png" />
      </div>
    </div>
    <van-popup v-model:show="state.visible" round position="bottom">
      <van-picker
        v-model="state.selected"
        :columns="basePickerOptions"
        title="请选择基地"
        @cancel="hideBasePicker"
        @confirm="onConfirm"
      />
    </van-popup>
  </div>
</template>

<style lang="less" scoped>
.select-base {
  padding-bottom: 30px;
  display: inline-block;
  &-value {
    display: inline-block;
    min-width: 60px;
    font-size: 20px;
    height: 24px;
    line-height: 24px;
    &.no-select-base {
      color: #999999;
      font-size: 16px;
    }
  }
  &-icon {
    display: inline-block;
    margin-left: 10px;
    width: 20px;
    > img {
      width: 20px;
      vertical-align: middle;
    }
  }
}
:deep(.van-picker-column__item.van-picker-column__item--selected) {
  font-weight: bold;
}
</style>

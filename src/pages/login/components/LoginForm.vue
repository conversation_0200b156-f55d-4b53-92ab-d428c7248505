<script lang="ts" setup>
import { showLoadingToast, showToast } from 'vant'
import { computed } from 'vue'
import { gucStore, LoginType } from '@/integration/guc'
import { loginStorage } from './baseConfig'

export interface LoginFormProps {
  type: LoginType
}

const props = defineProps<LoginFormProps>()

const loginTypeLabel = computed(() => {
  return props.type === LoginType.Domain ? '域账号' : '账号'
})

const state = reactive({
  showPassword: false,
  username: '',
  password: '',
  rememberPassword: false,
})

const emit = defineEmits(['success'])

function toggleShowPassword() {
  state.showPassword = !state.showPassword
}

function initLoginForm() {
  const savedConfig = loginStorage.value[props.type]

  if (savedConfig.remembered) {
    state.username = savedConfig.username
    state.password = savedConfig.password

    state.rememberPassword = true
  } else {
    clearForm()
  }
}

function clearForm() {
  state.username = ''
  state.password = ''
  state.rememberPassword = false
}

async function login() {
  if (!state.username) {
    showToast(`请输入${loginTypeLabel.value}`)
    return
  }
  if (!state.password) {
    showToast('请输入密码')
    return
  }

  const loading = showLoadingToast({
    forbidClick: true,
    message: '登陆中...',
    loadingType: 'spinner',
  })

  try {
    const formData = {
      type: props.type,
      username: state.username,
      password: state.password,
    }

    const result = await gucStore.login(formData)

    loginStorage.value[formData.type] = {
      username: state.username,
      password: state.password,
      remembered: state.rememberPassword,
    }
    loginStorage.value.lastLoginType = props.type

    emit('success', result)
    loading.close()
  } catch (error) {
    console.error(error)
  }
}

defineExpose({
  initLoginForm,
})
</script>

<template>
  <div class="login-text">
    <div class="login-text-english">Log In</div>
    <div class="login-text-china">{{ loginTypeLabel }}登录</div>
  </div>
  <div class="login-form-slot">
    <div class="login-form">
      <div class="field-item-block">
        <div class="field-item-block-label">{{ loginTypeLabel }}</div>
        <van-field clearable v-model="state.username" :placeholder="`请输入${loginTypeLabel}`" />
      </div>
      <div class="field-item-block">
        <div class="field-item-block-label">密码</div>
        <van-field
          clearable
          :type="state.showPassword ? 'text' : 'password'"
          v-model="state.password"
          placeholder="请输入密码"
        >
          <template #right-icon>
            <span @click="toggleShowPassword">
              <van-icon name="eye" v-if="state.showPassword" />
              <van-icon name="closed-eye" v-else />
            </span>
          </template>
        </van-field>
      </div>
      <div class="field-item-block">
        <van-checkbox v-model="state.rememberPassword" shape="square" checked-color="#00996B">
          <span class="record-password-text">记住密码</span>
        </van-checkbox>
      </div>
      <div class="login-btn">
        <van-button
          @click="login"
          color="#00996B"
          :disabled="!state.username || !state.password"
          block
        >
          登录
        </van-button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.login-text {
  padding: 0 15px;
  color: #333333;
  font-family: 'OPPOSans';
  font-style: normal;
  font-weight: 400;
  &-english {
    font-size: 24px;
    font-weight: 700;
  }
  &-china {
    font-size: 17px;
    padding-top: 6px;
  }
}
.login-form-slot {
  padding: 45px 15px 0 15px;
}

.login-form {
  position: relative;
  overflow: auto;

  .field-item-block {
    padding-bottom: 25px;
    &-label {
      padding-bottom: 8px;
      color: #666666;
    }
    :deep(.van-field) {
      border-radius: 4px;
      backdrop-filter: blur(27px);
      padding-top: 8px;
      padding-bottom: 8px;
      transition: border 300ms;
      &.focused {
        border: 1px solid #00996b;
      }
    }
    .record-password-text {
      color: #666666;
    }
  }
  .login-btn {
    padding-top: 15px;
  }
}
:deep(.van-checkbox__icon) {
  border-radius: 4px !important;
  overflow: hidden;
}
</style>

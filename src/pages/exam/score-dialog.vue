<script lang="ts" setup>
import { useVModel } from '@vueuse/core'

const props = defineProps<{
  visible: boolean
  examRes: {
    time?: string
    score?: string | number
  }
}>()
const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

const dialogProps = {
  cancelButtonText: '返回',
  showCancelButton: true,
  confirmButtonText: '查看',
  class: 'dialog-info',
}

const _visible = useVModel(props, 'visible', emit)
</script>

<template>
  <VanDialog v-model:show="_visible" v-bind="dialogProps" @confirm="emit('confirm')" @cancel="emit('cancel')">
    <div class="px-20px py-30px font-size-15px">
      <div class="flex items-center pb-20px color-[#333] font-bold">
        <div class="mr-6px h-24px w-24px">
          <img class="h-full w-full align-top" src="@/assets/icons/svg/dialog-info.svg">
        </div>
        <div>
          答题时间: <span class="font-size-17px color-[#333]">{{ examRes?.time ?? '-' }}</span>
        </div>
      </div>
      <div class="color-[#999]">
        分数: <span class="color-[#333]">{{ examRes?.score ?? '-' }}</span>分
      </div>
    </div>
  </VanDialog>
</template>

<style lang="less" scoped></style>

<script lang="ts" setup>
import VanFieldSelectPicKer from '@/components/van-field-select-picker.vue'
import {
  V1ManageQuestionBankColumnGetQuestionBankColumnListPost,
  V1ManageQuestionBankColumnMappingListPost,
  V1MobileHomeListPost,
  V1MobileStudyExamRecordStartPost,
} from '@/api/api.req'
import type { Opt } from '@/utils'
import { useCurrentUser } from '@/store/sysUser'
import type { QuestionBankColumnDTOElement } from '@/api/api.model'
import { ExamStatusEnum, PaperTypeEnum } from '@/enums/exam'

const router = useRouter()

const sysUserIns = useCurrentUser()

const testPaperId = ref<string[]>([])

const testPaperOpts = ref<Opt[]>([])

interface SelectOtherProps {
  opts: Opt[]
  selectColumnValue: string[]
}

type QuestionBankColumn = QuestionBankColumnDTOElement & SelectOtherProps

const questionBankColumn = ref<QuestionBankColumn[]>([])

/**
 * 加载所有题库字段配置
 */
async function loadAllQuestionBankColumn() {
  const resp = await V1ManageQuestionBankColumnGetQuestionBankColumnListPost({})
  const _list = (resp || []).map((o) => {
    return {
      ...o,
      selectColumnValue: [],
      opts: [],
    }
  })

  _list.forEach((o) => loadQuestionBankColumnMappingList(o))

  questionBankColumn.value = _list
}

async function loadQuestionBankColumnMappingList(item: QuestionBankColumn) {
  const resp = await V1ManageQuestionBankColumnMappingListPost({
    columnId: item.id!,
  })

  const _opts = [...new Set((resp || []).map((o) => o.columnValue!))]

  const opts = _opts.map((o) => ({
    text: o,
    value: o,
  }))
  item.opts = opts
}

const _propertyList = computed(() => {
  return questionBankColumn.value
    .filter((o) => Boolean(o.selectColumnValue?.[0]))
    .map((o) => {
      return {
        columnKey: o.columnKey!,
        columnValue: o.selectColumnValue[0],
      }
    })
})
/**
 * 加载待考试查询
 */
async function loadTobeTestPapers() {
  const userId = sysUserIns.sysUserGet?.id
  if (!userId) {
    return
  }
  testPaperOpts.value = []

  const resp = await V1MobileHomeListPost({
    userId,
    type: PaperTypeEnum.SKILL_INSPECTION,
    questionPropertyList: _propertyList.value,
    status: ExamStatusEnum.TO_BE,
  })

  if (unref(testPaperId)?.length) {
    const ids = resp!.map((i) => i.id)
    if (!ids.includes(unref(testPaperId)[0])) {
      testPaperId.value = []
    }
  }
  testPaperOpts.value = resp!.map((i) => ({
    text: i.paperName!,
    value: i.id!,
  }))
}
async function startWirtePaper() {
  /**
   * 开始训练后端记录初始信息
   */
  await V1MobileStudyExamRecordStartPost({
    id: unref(testPaperId)[0],
  })

  router.push({
    path: '/exam/start-exam',
    query: {
      id: unref(testPaperId)[0],
    },
  })
}

onMounted(() => {
  loadTobeTestPapers()
  loadAllQuestionBankColumn()
})

watch(() => [_propertyList.value], loadTobeTestPapers)
</script>

<template>
  <VanNavBar title="考试系统" left-text="返回" left-arrow @click-left="router.push('/exam')" />

  <div class="h-[calc(100vh-46px)] overflow-y-auto">
    <div class="overflow-hidden rounded-8px">
      <VanFieldSelectPicKer
        v-for="item in questionBankColumn"
        :key="item.id"
        v-model:picker-value="item.selectColumnValue"
        :label="item.columnName!"
        :columns="item.opts"
      />
      <VanFieldSelectPicKer
        v-model:picker-value="testPaperId"
        label="考卷"
        :columns="testPaperOpts"
      />
    </div>
    <div class="sticky bottom-0 bg-[#fff] px-10px py-10px">
      <VanButton type="primary" block :disabled="!testPaperId?.length" @click="startWirtePaper">
        确定考试
      </VanButton>
    </div>
  </div>
</template>

<style lang="less" scoped></style>

<script lang="ts" setup>
import type { V1LocationStudyCheckProjectIDUserIDGetResponseResult } from '@/api/api.model'
import { V1ManageTrainStudyRecordsPagePost } from '@/api/api.req'
import { TrainingProjectType, TrainingStatus, TrainingStatusOptions } from '@/enums/training'
import { useDynamicList } from '@/hooks/useDynamicList'
import { useLoading } from '@/hooks/useLoading'
import { currentUserInfo } from '@/store/sysUser'
import { getOptionLabel } from '@/utils'

const router = useRouter()
const route = useRoute()

const query = {
  type: route.query.type as TrainingProjectType,
  status: route.query.status as TrainingStatus[],
}

const trainingListState = useDynamicList({
  api: async (params) => {
    const resp = await V1ManageTrainStudyRecordsPagePost({
      ...params,
      data: {
        userId: currentUserInfo.value.id!,
        projectType: query.type as any,
        statusList: query.status,
      },
    })

    return {
      ...resp,
      records: resp.records?.map((item) => {
        return {
          ...item,
          id: item.id!,
          name: item.projectName!,
          tag: getOptionLabel(TrainingStatusOptions, item.status)!,
          linkText: '查看详情',
        }
      }),
    }
  },
})

const fetchData = useLoading(() => {
  trainingListState.reset()
  trainingListState.load()
})

fetchData()

function onClickBeTrainingItem(item: V1LocationStudyCheckProjectIDUserIDGetResponseResult) {
  router.push({
    path: '/training/detail',
    query: {
      id: item.id,
    },
  })
}
</script>

<template>
  <VanNavBar title="训练列表" />
  <div class="page-content px-10px">
    <VanPullRefresh
      :model-value="fetchData.isLoading"
      success-text="刷新成功!"
      @refresh="fetchData"
    >
      <!-- todo, rewrite style -->
      <CommonList :list-state="trainingListState" @click-item="onClickBeTrainingItem" />
    </VanPullRefresh>
  </div>
  <Footer />
</template>

<style lang="less" scoped></style>

<script lang="ts">
export enum EvaluateTypeQuery {
  TO_BE = 'to-be',
}
</script>

<script lang="ts" setup>
import type {
  PurpleAPIModel,
  V1LocationStudyCheckProjectIDUserIDGetResponseResult,
} from '@/api/api.model'
import { V1EvaluateTaskPagePost } from '@/api/api.req'
import { useDynamicList } from '@/hooks/useDynamicList'
import { useLoading } from '@/hooks/useLoading'
import { currentUserInfo } from '@/store/sysUser'

const router = useRouter()
const route = useRoute()

// todo
const query = {
  type: route.query.type as EvaluateTypeQuery,
}

const evaluateListState = useDynamicList({
  api: async (params) => {
    const resp = await V1EvaluateTaskPagePost({
      ...params,
      data: {
        reviewerUserId: currentUserInfo.value.id!,
        status: 0,
      },
    })

    return {
      ...resp,
      records: resp.records?.map((item) => {
        return {
          ...item,
          id: item.id!,
          name: item.evaluateName!,
          tag: '待评审',
          linkText: '去评审',
        }
      }),
    }
  },
})

const fetchData = useLoading(() => {
  evaluateListState.reset()
  evaluateListState.load()
})

fetchData()

function onClickBeTrainingItem(item: PurpleAPIModel) {
  router.push({
    path: '/evaluate/detail',
    query: {
      id: item.id,
    },
  })
}
</script>

<template>
  <VanNavBar title="评定列表" />
  <div class="page-content px-10px">
    <VanPullRefresh
      :model-value="fetchData.isLoading"
      success-text="刷新成功!"
      @refresh="fetchData"
    >
      <CommonList
        :list-state="evaluateListState"
        finish-text=""
        @click-item="onClickBeTrainingItem"
      />
    </VanPullRefresh>
  </div>
  <Footer />
</template>

<style lang="less" scoped></style>

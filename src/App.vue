<script lang="ts" setup>
import { Locale } from 'vant'
import zh from 'vant/es/locale/lang/zh-CN'

Locale.use('zh-CN', zh)
</script>

<template>
  <van-config-provider
    :theme-vars="{
      primaryColor: '#00996B',
      buttonDefaultBorderColor: 'transparent',
      dialogRadius: '16px',
      textEllipsisActionColor: '#00996B',
    }"
    theme-vars-scope="global"
  >
    <div>
      <RouterView />
    </div>
  </van-config-provider>
</template>

<style lang="less">
.slide-fade-enter-active {
  transition: all 0.1s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.1s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
</style>

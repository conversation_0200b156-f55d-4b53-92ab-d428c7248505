# Assets 说明

`icons` 文件夹中的 svg 会自动加载，使用方式：

```jsx
<SvgIcon name="logo" size="24" />
```

**!注意**，`name` 必须是完整的文件名称，不可使用字符串拼接，例如：

```jsx
// ❌ 错误
const name = 'lo' + 'go'
<SvgIcon :name="name" size="24" />
// ✅ 正确
const name = 'logo'
<SvgIcon :name="name" size="24" />

// ❌ 错误
const name =  'nav-' + x ? '1': '2'
<SvgIcon :name="name" size="24" />
// ✅ 正确
const name =  x ? 'nav-1' : 'nav-2'
<SvgIcon :name="name" size="24" />
```

<script lang="ts" setup>
import type { SvgIconNames } from 'types/shim'
import UnSvgIcon from '~virtual/svg-component';

const props = defineProps<{
  name: SvgIconNames
  /**
   *  example:
   *
   * - size='1em'
   * - size='12px'
   * - size='12'
   * - size='12px 24px'
   * - size='12 24'
   */
  size?: string | number
}>()

const defaultSize = 20 as string | number

const styleSize = computed(() => {
  const _size = {
    width: defaultSize,
    height: defaultSize,
  }

  if (props.size == null) return _size

  if (typeof props.size === 'number') {
    _size.width = _size.height = props.size

    return _size
  }

  const [width, height] = props.size.split(/[ x]+/) || []

  _size.width = convertToStyleValue(width || _size.width)
  _size.height = convertToStyleValue(height || _size.width)

  return _size
})

function convertToStyleValue(n: string | number) {
  return /^[\d.]+$/.test(n.toString()) ? `${n}px` : n
}

const style = computed(() => {
  return {
    ...styleSize.value,
  }
})
</script>

<template>
  <span class="inline-flex">
    <UnSvgIcon :style="style" :name="props.name" />
  </span>
</template>

<style lang="less" scoped></style>

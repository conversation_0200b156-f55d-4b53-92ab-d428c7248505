<script lang="ts" setup>
import { gucStore } from '@/integration/guc'
import { currentUserInfo } from '@/store/sysUser'

async function loginOut() {
  gucStore.logoutConfirm()
}
</script>

<template>
  <div class="mx-10px mt-12px rounded-3px px-8px py-40px">
    <div class="flex justify-center pb-20px">
      <div class="h-60px w-60px overflow-hidden rounded-50%">
        <img class="h-full w-full" src="@/assets/guc-avatar.svg" />
      </div>
    </div>
    <div class="userInfo-name">
      {{ currentUserInfo.name || currentUserInfo.account }}
    </div>
  </div>
  <div class="pt-100px font-size-14px">
    <div class="flex justify-center color-[#666]">用户未同步到该系统!</div>
    <div class="flex justify-center pt-10px">
      <span class="primary-color" @click="loginOut">退出登录</span>
    </div>
  </div>
</template>

<style lang="less" scoped>
.card-item {
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
}

.userInfo-name {
  @apply w-full border-1px border-color-[#f4f6f8] border-t-solid pt-10px font-size-17px color-[#666] flex justify-center;
}
</style>

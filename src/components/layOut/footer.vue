<script lang="ts" setup>
import Home from '@/assets/icons/menus/Home.svg'
import HomeActive from '@/assets/icons/menus/active/Home.svg'

import Profile from '@/assets/icons/menus/Profile.svg'
import ProfileActive from '@/assets/icons/menus/active/Profile.svg'

import ClockCircle from '@/assets/icons/menus/clock-circle.svg'
import ClockCircleActive from '@/assets/icons/menus/active/clock-circle.svg'

import Read from '@/assets/icons/menus/read.svg'
import ReadActive from '@/assets/icons/menus/active/read.svg'

import Schedule from '@/assets/icons/menus/schedule.svg'
import ScheduleActive from '@/assets/icons/menus/active/schedule.svg'

interface TabbarItem {
  active: string
  inactive: string
  router: string
  name: string
}

const router = useRouter()

const route = useRoute()

const tabbars: TabbarItem[] = [
  {
    active: HomeActive,
    inactive: Home,
    router: '/home',
    name: '首页',
  },
  {
    active: ReadActive,
    inactive: Read,
    router: '/learning',
    name: '学习',
  },
  {
    active: ClockCircleActive,
    inactive: ClockCircle,
    router: '/appointment',
    name: '预约',
  },
  {
    active: ScheduleActive,
    inactive: Schedule,
    router: '/exam',
    name: '考试',
  },
  // {
  //   active: AssessmentExamActive,
  //   inactive: AssessmentExam,
  //   router: '/on-site-assessment',
  //   name: '现场考核',
  // },
  {
    active: ProfileActive,
    inactive: Profile,
    router: '/mine',
    name: '我的',
  },
]

const activeRoute = ref<number>(calcTabIdx())

function calcTabIdx() {
  return route.path ? tabbars.findIndex((i) => i.router === route.path) : 0
}

function jumpPage(item: TabbarItem) {
  router.push(item.router)
}
</script>

<template>
  <VanTabbar v-model="activeRoute" active-color="#00996B">
    <VanTabbarItem v-for="(item, idx) in tabbars" :key="idx" @click="jumpPage(item)">
      <span>{{ item.name }}</span>
      <template #icon="props">
        <img class="size-24px!" :src="props.active ? item.active : item.inactive" />
      </template>
    </VanTabbarItem>
  </VanTabbar>
</template>

<style lang="less" scoped></style>

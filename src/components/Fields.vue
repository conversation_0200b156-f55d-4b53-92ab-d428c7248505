<script lang="ts" setup>
defineProps<{
  fields: { value: any, label: string }[]
}>()
</script>

<template>
  <div class="px-16px">
    <div v-for="item in (fields || [])" :key="item.label" class="item-field flex py-14px font-size-15px">
      <div class="w-100px break-all color-[#999]">
        {{ item.label }}
      </div>
      <div class="flex-1 break-all color-[#666] lh-[20px]" v-html="item.value ?? '-'" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.item-field {
  border-bottom: 0.5px solid rgba(235, 237, 240);
  &:last-child {
    border-bottom: 0px solid transparent;
  }
}
</style>

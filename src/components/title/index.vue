<script setup lang="ts">
const props = defineProps({
  text: {
    type: String,
    default: '请设置标题',
  },
  fontSize: {
    type: [String, Number],
  },
})
const defSize = 15

const font = computed((): number => {
  return props.fontSize ? Number(`${props.fontSize}`.toLowerCase().replace('px', '')) : defSize
})
</script>

<template>
  <div class="w-full flex lh-normal">
    <div
      class="bg-[#24b276]"
      :style="{
        width: `${Math.floor(font / 5)}px`,
        margin: `${Math.floor(font / 5)}px 0`,
        borderTopRightRadius: `${Math.floor(font / 4)}px`,
        borderBottomRightRadius: `${Math.floor(font / 4)}px`,
        minWidth: '3px',
      }"
    />
    <div
      class="w-full flex-1 color-[#666] font-bold"
      :style="{
        fontSize: `${font}px`,
        paddingLeft: `${Math.ceil(font / 2)}px`,
      }"
    >
      <slot>{{ text }}</slot>
    </div>
  </div>
</template>

<style scoped lang="less"></style>

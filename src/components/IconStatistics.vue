<script lang="ts" setup>
import type { SvgIconNames } from 'types/shim'

export interface IconStatisticsProps {
  value?: string | number
  unit?: string
  title?: string
  icon: SvgIconNames
  tag?: string

  /**
   * @default 'vertical'
   */
  layout?: 'vertical' | 'horizontal'
}

defineProps<IconStatisticsProps>()
</script>

<template>
  <div
    class="icon-statistics flex flex-col gap-2 items-center"
    :class="{ 'is-horizontal': layout === 'horizontal' }"
  >
    <div class="icon">
      <SvgIcon :name="icon" />
    </div>
    <div class="content flex flex-col gap-2">
      <div class="title">
        {{ title }}
      </div>
      <div v-if="tag" class="h-24px flex items-center">
        <ColorTag>{{ tag }}</ColorTag>
      </div>
      <div v-else class="h-24px flex items-end">
        <div class="value">{{ value }}</div>
        <div class="unit" v-if="unit">{{ unit }}</div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.icon-statistics {
  &.is-horizontal {
    flex-direction: row;
    .content {
      align-items: start;
    }
  }

  .content {
    @apply flex items-center;
  }
}

.icon {
  @size: 40px;
  width: @size;
  height: @size;
  display: inline-flex;

  justify-content: center;
  align-items: center;

  border-radius: 6px;
  background: #f3f3f3;
}

.value {
  color: rgba(0, 0, 0, 0.9);
  font-size: 20px;
  margin-right: 4px;
}
</style>

<script lang="ts" setup>
withDefaults(
  defineProps<{
    tip?: string
  }>(),
  {
    tip: '暂无数据',
  },
)
</script>

<template>
  <div class="py-20px">
    <div
      class="flex flex justify-center"
    >
      <div class="h-80px w-80px">
        <img class="h-full w-full" src="@/assets/empty.svg">
      </div>
    </div>
    <div class="flex justify-center pt-20px font-size-14px color-[#999]">
      {{ tip }}
    </div>
  </div>
</template>

<style lang="less" scoped>
</style>

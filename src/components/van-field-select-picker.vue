<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import type { PickerOption } from 'vant'
import type { Numeric } from 'vant/lib/utils/basic.d.ts'
import EmptyData from '@/components/EmptyData.vue'

export type NumericItem = Numeric

const props = withDefaults(defineProps<{
  columns: PickerOption[]
  pickerValue: Numeric[]
  label: string
  ableSearch?: boolean
}>(), {
  ableSearch: true,
})

const emit = defineEmits(['update:pickerValue'])

const showPicker = ref<boolean>(false)

const vPickerValue = useVModel(props, 'pickerValue', emit)

const keyWord = ref<string>('')

const fieldValue = computed(() => {
  return vPickerValue.value?.length ? props.columns.find(i => i.value === vPickerValue.value![0])?.text : undefined
})
const calcColumns = computed(() => {
  const _v = keyWord.value ? keyWord.value.trim() : ''
  return !_v ? props.columns : props.columns.filter(i => i.text && `${i.text}`.includes(_v))
})

function onConfirm(params: { selectedValues: Numeric[] }) {
  showPicker.value = false
  vPickerValue.value = params.selectedValues || []
}

function clear() {
  vPickerValue.value = []
}

watch(() => showPicker.value, (value) => {
  if (value) {
    keyWord.value = ''
  }
})
</script>

<template>
  <div class="relative">
    <VanField
      v-model="fieldValue"
      is-link
      readonly
      :label="label"
      placeholder="请选择"
      autocomplete="off"
      @click="showPicker = true"
    />
    <VanPopup v-model:show="showPicker" destroy-on-close round position="bottom">
      <VanPicker
        :key="`${columns.map(o => o.value).join('_')}`"
        :model-value="vPickerValue"
        :columns="calcColumns"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      >
        <template v-if="ableSearch" #option="item">
          <div class="w-100% break-all text-center">
            <VanTextEllipsis :content="item?.text || '-'">
              <template #action="{ expanded }">
                <span v-if="!expanded"><VanIcon name="arrow-down" /></span>
              </template>
            </VanTextEllipsis>
          </div>
        </template>
        <template v-if="ableSearch" #columns-top>
          <div class="px-15px">
            <van-field
              v-model="keyWord"
              class="search-input"
              clearable
              placeholder="请输入关键字搜索"
              autocomplete="off"
            />
          </div>
        </template>
        <template #empty>
          <div class="h-[264px] flex items-center justify-center">
            <EmptyData />
          </div>
        </template>
      </VanPicker>
    </VanPopup>
    <span v-if="vPickerValue?.length" class="absolute right-35px top-[calc(50%-10px)]" @click.stop="clear"><VanIcon name="clear" color="#999" /></span>
  </div>
</template>

<style lang="less" scoped>
@import '../theme/vars.less';
:deep(.search-input.van-field) {
  border-radius: 4px;
  padding-top: 4px;
  padding-bottom: 4px;
  border: 1px solid #f5f6f7;
}
:deep(.van-picker-column__item--selected) {
  color: @primary-color;
}
</style>

<script lang="ts" setup>
// https://github.com/zxing-js/library?tab=readme-ov-file#limitations
import { BarcodeFormat, DecodeHintType, BrowserMultiFormatReader, Result } from '@zxing/library'

const hints = new Map()
const formats = [BarcodeFormat.QR_CODE, BarcodeFormat.DATA_MATRIX /*, ...*/]

hints.set(DecodeHintType.POSSIBLE_FORMATS, formats)

const emit = defineEmits<{
  scanned: [result: Result]
}>()

const reader = new BrowserMultiFormatReader()

const videoEl = ref<HTMLVideoElement>()

const visible = ref(false)

async function startScan() {
  visible.value = true

  try {
    const result = await reader.decodeOnceFromVideoDevice(undefined, videoEl.value)
    emit('scanned', result)
    return result
  } catch (error) {
    console.warn(error)
  } finally {
    close()
  }
}

function close() {
  reader.reset()
  visible.value = false
}

defineExpose({
  startScan,
})
</script>

<template>
  <div v-show="visible" class="scanner fixed size-screen z-9999 top-0 left-0 bg-black">
    <video class="size-full" ref="videoEl"></video>
    <div class="close absolute right-4 top-4 text-red" @click="close">X</div>
  </div>
</template>

<style lang="less" scoped></style>

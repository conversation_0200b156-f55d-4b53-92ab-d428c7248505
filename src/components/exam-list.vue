<script lang="ts" setup>
import { debounce } from 'lodash-es'
import TestPaperItem, { type TestPaperItemProps } from '@/pages/components/test-paper-item.vue'
import { V1MobileHomePagePost } from '@/api/api.req'
import { useCurrentUser } from '@/store/sysUser'
import EmptyData from '@/components/EmptyData.vue'
import type {
  V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult,
  V1MobileHomePagePostRequestBodyData,
} from '@/api/api.model'

export interface ExamListProps {
  paperItemConfig?: Omit<TestPaperItemProps, 'testPaper'>
  params: Pick<V1MobileHomePagePostRequestBodyData, 'type' | 'statusList'>
}

const props = defineProps<ExamListProps>()

const emit = defineEmits(['clickExamItem'])

const sysUserIns = useCurrentUser()

type PaperRecord = V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult

const vanListProps = reactive({
  loading: true,
  finished: false,
  data: [] as PaperRecord[],
})

const pullRefreshIng = ref<boolean>(false)

const PAGE_SIZE = 20

const currentPage = ref<number>(0)

async function loadData(isPullRefresh?: boolean) {
  const userId = sysUserIns.sysUserGet?.id
  if (!userId) {
    vanListProps.loading = false
    return
  }
  vanListProps.loading = true

  const params = {
    pageSize: PAGE_SIZE,
    currentPage: currentPage.value + 1,
    data: {
      ...props.params,
      userId,
    },
  }
  const resp = await V1MobileHomePagePost(params)

  pullRefreshIng.value = false

  currentPage.value = params.currentPage

  const records = resp.records || []

  vanListProps.data = isPullRefresh ? records : [...vanListProps.data, ...records]

  vanListProps.loading = false

  vanListProps.finished = currentPage.value >= resp.totalPages!
}

function clear() {
  vanListProps.finished = false
  vanListProps.loading = true
  currentPage.value = 0
}

function refresh() {
  clear()
  loadData(true)
}

const scrollLoad = debounce(() => {
  loadData()
}, 200)

onMounted(loadData)

const isShowEmpty = computed(() => !vanListProps.data?.length && !vanListProps.loading)
</script>

<template>
  <div class="box-border overflow-y-auto px-10px">
    <VanPullRefresh v-model="pullRefreshIng" success-text="刷新成功!" @refresh="refresh">
      <VanList
        :loading="vanListProps.loading"
        :finished="vanListProps.finished"
        :finished-text="isShowEmpty ? '' : '没有更多了'"
        :offset="20"
        :immediate-check="false"
        @load="scrollLoad"
      >
        <div v-for="item in vanListProps.data" :key="item.id" class="pb-10px">
          <TestPaperItem
            v-bind="paperItemConfig"
            :test-paper="item"
            @click-detail="emit('clickExamItem', item)"
          />
        </div>
      </VanList>
    </VanPullRefresh>
    <EmptyData v-if="isShowEmpty" />
  </div>
</template>

<style lang="less" scoped>
.fixed-bottom {
  @apply fixed bottom-50px w-full border-1px border-color-[#f4f6f8] border-t-solid bg-[#fff] px-8px py-10px box-border;
}
</style>

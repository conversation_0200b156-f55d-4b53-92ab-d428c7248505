<script lang="ts" setup>
import type { ColorTagProps } from './ColorTag.vue'

export interface CommonListItem {
  linkText?: string
  tagColor?: ColorTagProps['color']
  tagContent?: string
  title?: string
  showArrow?: boolean
}

const props = defineProps<CommonListItem>()
</script>

<template>
  <div class="common-list-item">
    <div class="flex flex-col gap-2 w-full">
      <div v-if="title" class="title">
        {{ title }}
      </div>
      <div class="list-content">
        <slot name="prefix">
          <div v-if="tagContent" class="prefix">
            <ColorTag :color="tagColor">
              {{ tagContent }}
            </ColorTag>
          </div>
        </slot>
        <div class="content">
          <slot></slot>
        </div>
        <slot name="link">
          <div v-if="linkText" class="link-text-wrapper">
            <span class="link-text">{{ linkText }}</span>
            <svg v-if="showArrow" class="arrow-icon" viewBox="0 0 6 9" fill="none">
              <path
                d="M4.45962 0L5.37886 0.919239L1.83848 4.45962L5.37886 8L4.45962 8.91924L0 4.45962L4.45962 0Z"
                fill="currentColor"
                fill-opacity="0.4"
                fill-rule="evenodd"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.common-list-item {
  display: flex;
  align-items: center;
  padding: 8px 0 16px;
  border-bottom: 0.5px solid #ddd;

  .title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    font-weight: 500;
  }

  .list-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .prefix {
    :deep(.van-tag) {
      background-color: rgba(252, 136, 0, 0.24);
      color: #fc8800;
      font-size: 12px;
      line-height: 20px;
      padding: 0 4px;
      border-radius: 2px;
      height: 18px;
      border: none;
    }
  }

  .link-text-wrapper {
    display: flex;
    align-items: center;
    gap: 2px;

    .link-text {
      font-size: 11px;
      color: #999999;
      margin-right: 4px;
    }

    .arrow-icon {
      width: 6px;
      height: 10px;
      transform: rotate(180deg);
    }
  }
}
</style>

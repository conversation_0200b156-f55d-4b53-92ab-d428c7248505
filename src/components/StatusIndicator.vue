<script lang="ts" setup>
export interface StatusIndicatorProps {
  color?: string
  size?: number
}

const props = defineProps<StatusIndicatorProps>()

const style = computed(() => ({
  backgroundColor: props.color || '#2F7DF3',
  width: `${props.size || 8}px`,
  height: `${props.size || 8}px`,
}))

const wrapperStyle = computed(() => ({
  paddingLeft: `${(props.size || 8) + 4}px`,
}))
</script>

<template>
  <div class="status-indicator" :style="wrapperStyle">
    <slot></slot>
    <div class="indicator" :style="style"></div>
  </div>
</template>

<style lang="less" scoped>
.status-indicator {
  position: relative;

  .indicator {
    position: absolute;
    z-index: 1;
    left: 0;
    top: 50%;
    transform: translateY(-50%);

    border-radius: 99px;
  }
}
</style>

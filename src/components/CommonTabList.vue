<script lang="ts" setup>
export interface ListTabItem {
  id: string
  name: string
}

export interface CommonTabListProps {
  tabs: ListTabItem[]
  activeTabId: string
}

const props = defineProps<CommonTabListProps>()

const emit = defineEmits<{
  change: [tab: ListTabItem]
}>()
</script>

<template>
  <div class="common-tab-list">
    <div class="tabs">
      <div
        v-for="tab in tabs"
        :key="tab.id"
        class="tab-item"
        :class="{ 'tab-active': tab.id === activeTabId }"
        @click="emit('change', tab)"
      >
        <span>{{ tab.name }}</span>
        <div v-if="tab.id === activeTabId" class="active-indicator" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.common-tab-list {
  background-color: #ffffff;
  position: relative;
  width: 100%;
  border-bottom: 0.5px solid #e7e7e7;

  &.sticky {
    position: sticky;
    top: 10px;
    z-index: 10;
  }

  .tabs {
    display: flex;
    flex-direction: row;
    align-items: start;
    justify-content: start;
    width: 100%;
  }

  .tab-item {
    flex: 1;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 0 16px;
    cursor: pointer;
    font-size: 16px;
    font-family: 'PingFang SC', sans-serif;
    line-height: 24px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: 400;

    &.tab-active {
      color: #00996b;
      font-weight: 600;
    }
  }

  .active-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 3px;
    background-color: #00996b;
    border-radius: 999px;
  }
}
</style>

<script lang="ts" setup>
export interface ColorTagProps {
  color?: 'yellow'
}

defineProps<ColorTagProps>()
</script>

<template>
  <div class="tag">
    <slot></slot>
  </div>
</template>

<style lang="less" scoped>
.tag {
  border-radius: 2px;
  font-size: 14px;
  padding: 4px 8px;

  color: #fc8800;
  background: rgba(252, 136, 0, 0.24);

  &.yellow {
    color: #fc8800;
    background: rgba(252, 136, 0, 0.24);
  }
}
</style>

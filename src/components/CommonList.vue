<script lang="ts" generic="T extends ListItem" setup>
import type { UseDynamicListReturn } from '@/hooks/useDynamicList'
import { watchImmediate } from '@vueuse/core'

export interface ListItem {
  id: string
  name: string
  tag: string
  linkText?: string
}

export interface CommonListProps<T> {
  listState: UseDynamicListReturn<T>
  title?: string
  finishText?: string
}

const props = defineProps<CommonListProps<T>>()

watchImmediate(
  () => props.listState,
  () => {
    props.listState.forceLoadTheFirstPage()
  },
)

const emit = defineEmits<{
  'click-item': [item: T]
}>()
</script>

<template>
  <div>
    <div v-if="title" class="title">{{ title }}</div>
    <VanList
      v-bind="listState.props.value"
      :finished-text="finishText ?? '没有更多了'"
      :offset="20"
      @load="listState.load"
      class="list"
    >
      <CommonListItem
        v-for="item in listState.data.value"
        :key="item.id"
        @click="emit('click-item', item as T)"
        :title="item.name"
        :tagContent="item.tag"
        :linkText="item.linkText"
        :showArrow="true"
      />
    </VanList>
  </div>
</template>

<style lang="less" scoped>
.title {
  color: var(--text-icon-font-gy-190, rgba(0, 0, 0, 0.9));
  font-weight: bold;
  margin-bottom: 8px;
}
</style>

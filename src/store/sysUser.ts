import { defineStore } from 'pinia'
import type { V1LocationHomeCurrentUserGetResponseResult } from '@/api/api.model'
import { V1LocationHomeCurrentUser, V1MobileHomeLoginSync } from '@/api/api.req'
import { gucStore } from '@/integration/guc'

type SysUser = V1LocationHomeCurrentUserGetResponseResult
interface SysUserMap {
  sysUser?: SysUser
}

export const useCurrentUser = defineStore('sys-user', {
  state: (): SysUserMap => ({
    sysUser: undefined,
  }),
  getters: {
    sysUserGet: (state) => state.sysUser,
  },
  actions: {
    setSysUser(sysUser: SysUser) {
      this.sysUser = sysUser
    },
    async initialize() {
      if (this.$state.sysUser?.id) {
        return true
      }

      if (!gucStore.getToken()) {
        return true
      }

      try {
        const userInfo = await V1LocationHomeCurrentUser()

        this.setSysUser(userInfo || {})

        if (userInfo) {
          V1MobileHomeLoginSync()
        }
        return true
      } catch (_err: any) {
        console.error(_err)
      }
    },
    clear() {
      this.$state.sysUser = undefined
    },
  },
})

export const currentUserInfo = computed(() => useCurrentUser().sysUserGet || {})

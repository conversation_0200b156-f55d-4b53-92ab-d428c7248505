import { createRouter, createWebHashHistory } from 'vue-router'
import { createPermissionGuard } from './permission'
import { createMetaGuard } from './meta'
import { routes, handleHotUpdate } from 'vue-router/auto-routes'

export const history = createWebHashHistory()

export const router = createRouter({
  history,
  routes,
})

createPermissionGuard(router)
createMetaGuard(router)

if (import.meta.hot) {
  handleHotUpdate(router)
}

import type { Router } from 'vue-router'
import { monitorRouteChange } from '@/integration/online-monitor'
import { useCurrentUser } from '@/store/sysUser'

export const ROUTE_WHITE_LIST: string[] = ['/exception', '/login', '/']

export function createPermissionGuard(router: Router) {
  router.beforeEach(async (to, _from, next) => {
    if (ROUTE_WHITE_LIST.includes(to.path)) {
      next()
      return
    }

    const ok = await useCurrentUser().initialize()

    if (!ok) {
      next('/login')
    }

    monitorRouteChange(to.fullPath)

    next()
  })
}

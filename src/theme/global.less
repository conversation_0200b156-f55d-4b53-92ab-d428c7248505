@import './vars.less';

html,
body {
  background-color: #f5f6f7 !important;
}

html {
  color: #666;
  overflow: hidden;
}

button,
img,
input {
  outline: none;
}

table {
  font-size: inherit;
}

a {
  &.text {
    color: @gray-9;

    &:hover {
      color: @primary-color;
    }
  }

  &.icon {
    color: #afafb6;

    &:hover {
      color: @primary-color;
    }
  }
}

button {
  &:not(:disabled).text {
    color: @gray-9;
    background: none;

    &:hover {
      color: @primary-color;
    }
  }
}

* {
  box-sizing: border-box;

  scrollbar-width: thin;
}

*:focus,
*:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}


.preview-modal-wrap {
  display: flex;
  align-items: center;

  .ant-modal {
    top: 0;
    padding-bottom: 0;

    .ant-modal-body {
      .ant-modal-confirm-body>.anticon+.ant-modal-confirm-title+.ant-modal-confirm-content {
        margin-left: 0
      }

      padding: 20px;
      max-height: initial;

      .anticon {
        display: none;
      }
    }

    .ant-modal-confirm-btns {
      display: none;
    }
  }
}

.ant-empty {
  .ant-empty-image {
    background: no-repeat center url('@/assets/empty.svg');

    .ant-empty-img-default {
      opacity: 0;
    }
  }
}

.primary-color {
  color: @primary-color !important;
}

.border-primary-color {
  border-color: @primary-color !important;
}

.text-btn {
  cursor: pointer;
  .primary-color()
}

.card {
  background-color: white;
  padding: 16px;
  margin-bottom: 16px;
}

#luckysheetloadingdata {
  opacity: 0.5;
  width: 80vw !important;
  height: 60vh !important;
  left: 50% !important;
  top: 20% !important;
  transform: translate(-50%);
}
.ant-modal-confirm-body .ant-modal-confirm-title{
  font-size: 18px !important;
}
.del-model-cancel-btn{
  background-color: #f3f3f3 !important;
}
.del-model-ok-btn{
  background-color: #F76F5D !important;
  border-color: transparent  !important;
}
.table-title-slot{
  padding: 0 16px;
}
.ant-progress-bg {
  background-color: @primary-color !important;
}
.ant-btn > .anticon + span, .ant-btn > span + .anticon {
  margin-left: 4px !important;
}

.van-toast {
  background: var(--van-toast-background) !important;
}

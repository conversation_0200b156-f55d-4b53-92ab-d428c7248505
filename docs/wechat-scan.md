# 微信扫码功能集成文档

本文档介绍如何在项目中使用微信JS-SDK的扫码功能。

## 功能特性

- ✅ 自动检测微信环境
- ✅ 动态加载微信JS-SDK
- ✅ 自动获取微信配置
- ✅ 支持二维码和一维码扫描
- ✅ 优雅的错误处理
- ✅ 开发环境模拟扫码
- ✅ TypeScript 类型支持

## 文件结构

```
src/
├── utils/wechat.ts          # 微信SDK工具类
├── api/wechat.ts            # 微信相关API接口
├── config/wechat.ts         # 微信配置文件
├── components/QrScanner/    # 扫码组件
└── pages/wechat-scan-demo.vue # 演示页面
```

## 快速开始

### 1. 使用扫码组件

```vue
<template>
  <QrScanner
    button-text="扫一扫"
    button-type="primary"
    :on-success="handleScanSuccess"
    :on-error="handleScanError"
  />
</template>

<script setup lang="ts">
import QrScanner from '@/components/QrScanner/index.vue'

function handleScanSuccess(result: string, parsedResult?: any) {
  console.log('扫码成功:', result, parsedResult)
}

function handleScanError(error: Error) {
  console.error('扫码失败:', error)
}
</script>
```

### 2. 直接使用微信SDK

```typescript
import { wechatSDK, configWechat, scanQRCode } from '@/utils/wechat'
import { initWechatSDK } from '@/api/wechat'

// 初始化微信SDK
async function initWechat() {
  try {
    const wxConfig = await initWechatSDK()
    await configWechat(wxConfig)
    console.log('微信SDK初始化成功')
  } catch (error) {
    console.error('微信SDK初始化失败:', error)
  }
}

// 扫码
async function scan() {
  try {
    const result = await scanQRCode({
      needResult: 1,
      scanType: ['qrCode', 'barCode']
    })
    console.log('扫码结果:', result.resultStr)
  } catch (error) {
    console.error('扫码失败:', error)
  }
}
```

## 组件属性

### QrScanner 组件

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| buttonText | string | '扫一扫' | 按钮文字 |
| buttonType | string | 'primary' | 按钮类型 |
| buttonSize | string | 'normal' | 按钮大小 |
| buttonClass | string | '' | 按钮样式类 |
| disabled | boolean | false | 是否禁用 |
| onSuccess | function | - | 扫码成功回调 |
| onError | function | - | 扫码失败回调 |

## API 接口

### 获取微信配置

后端需要提供以下接口来获取微信JS-SDK配置：

```typescript
POST /v1/wechat/js-sdk/config

// 请求参数
{
  "url": "https://your-domain.com/page" // 当前页面URL
}

// 响应数据
{
  "appId": "wx1234567890abcdef",
  "timestamp": **********,
  "nonceStr": "random-string",
  "signature": "signature-string",
  "jsApiList": ["scanQRCode", "chooseImage", ...]
}
```

### 微信签名算法

后端需要按照微信官方文档实现签名算法：

1. 获取 access_token
2. 获取 jsapi_ticket
3. 生成签名

参考：https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#62

## 配置说明

### 微信公众号配置

1. 登录微信公众平台
2. 进入"设置与开发" -> "基本配置"
3. 配置服务器域名，添加你的域名到"JS接口安全域名"

### 环境变量

```env
# 微信相关配置
WECHAT_APP_ID=your_app_id
WECHAT_APP_SECRET=your_app_secret
```

## 扫码结果处理

组件会自动解析扫码结果，支持以下格式：

### JSON 格式

```json
{
  "type": "workstation_info",
  "workstationId": "123456",
  "workstationName": "工位01",
  "levelName": "车间A",
  "userId": "789",
  "userName": "张三",
  "projectId": "456",
  "projectName": "装配项目"
}
```

### URL 格式

```
https://example.com/page?param=value
```

### 纯文本格式

```
任意文本内容
```

## 错误处理

常见错误及解决方案：

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| invalid url domain | 域名未配置 | 在微信公众平台添加域名到安全域名列表 |
| invalid signature | 签名错误 | 检查后端签名算法实现 |
| permission denied | 权限不足 | 检查jsApiList配置 |
| function not exist | 功能不存在 | 检查微信客户端版本 |

## 开发调试

### 开启调试模式

```typescript
const wxConfig = await initWechatSDK(true) // 开启调试模式
```

### 微信开发者工具

1. 下载微信开发者工具
2. 使用"公众号网页调试"功能
3. 输入页面URL进行调试

### 真机调试

1. 在微信中打开页面
2. 开启调试模式查看控制台日志
3. 使用 vConsole 进行移动端调试

## 注意事项

1. **域名限制**：只能在配置的安全域名下使用
2. **HTTPS要求**：生产环境必须使用HTTPS
3. **微信版本**：确保微信客户端版本支持所需功能
4. **签名有效期**：jsapi_ticket有效期为7200秒，需要定期刷新
5. **并发限制**：access_token和jsapi_ticket有调用频率限制

## 演示页面

访问 `/wechat-scan-demo` 查看完整的演示页面，包含：

- 不同样式的扫码按钮
- 扫码结果展示
- 错误处理演示
- 扫码记录管理

## 部署指南

### 1. 后端部署

参考 `docs/backend-example.js` 文件，实现微信JS-SDK配置接口：

```bash
# 安装依赖
npm install express axios crypto

# 设置环境变量
export WECHAT_APP_ID=your_app_id
export WECHAT_APP_SECRET=your_app_secret

# 启动服务
node docs/backend-example.js
```

### 2. 前端部署

确保前端项目部署在HTTPS域名下，并在微信公众平台配置安全域名。

### 3. 微信公众平台配置

1. 登录 [微信公众平台](https://mp.weixin.qq.com/)
2. 进入"设置与开发" -> "基本配置"
3. 在"JS接口安全域名"中添加你的域名
4. 保存配置

### 4. 测试验证

1. 在微信中打开页面
2. 点击扫码按钮
3. 扫描二维码或条形码
4. 查看扫码结果

## 故障排除

### 常见问题

1. **invalid url domain**
   - 检查域名是否已添加到微信公众平台的安全域名列表
   - 确保使用的是完整的域名（包括协议）

2. **invalid signature**
   - 检查后端签名算法实现
   - 确保URL参数正确（不包含#及后面的内容）
   - 检查时间戳是否正确

3. **permission denied**
   - 检查jsApiList是否包含所需的接口
   - 确保微信客户端版本支持该功能

4. **function not exist**
   - 更新微信客户端到最新版本
   - 检查接口名称是否正确

### 调试技巧

1. 开启调试模式查看详细错误信息
2. 使用微信开发者工具进行调试
3. 检查网络请求是否正常
4. 查看浏览器控制台日志

## 更新日志

### v1.0.0
- 初始版本
- 支持微信扫码功能
- 自动环境检测
- 完整的错误处理
- 提供完整的后端示例
- 支持TypeScript类型定义

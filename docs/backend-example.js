/**
 * 微信JS-SDK后端接口示例 (Node.js/Express)
 *
 * 注意：这只是一个示例实现，实际项目中需要根据具体框架调整
 */

const crypto = require('node:crypto')
const axios = require('axios')
const express = require('express')

const app = express()
app.use(express.json())

// 微信配置
const WECHAT_CONFIG = {
  appId: process.env.WECHAT_APP_ID || 'your_app_id',
  appSecret: process.env.WECHAT_APP_SECRET || 'your_app_secret',
}

// 缓存access_token和jsapi_ticket
let tokenCache = {
  access_token: null,
  access_token_expires: 0,
  jsapi_ticket: null,
  jsapi_ticket_expires: 0,
}

/**
 * 获取access_token
 */
async function getAccessToken() {
  const now = Date.now()

  // 如果缓存的token还有效，直接返回
  if (tokenCache.access_token && now < tokenCache.access_token_expires) {
    return tokenCache.access_token
  }

  try {
    const response = await axios.get('https://api.weixin.qq.com/cgi-bin/token', {
      params: {
        grant_type: 'client_credential',
        appid: WECHAT_CONFIG.appId,
        secret: WECHAT_CONFIG.appSecret,
      },
    })

    const { access_token, expires_in } = response.data

    if (!access_token) {
      throw new Error(`获取access_token失败: ${JSON.stringify(response.data)}`)
    }

    // 缓存token，提前5分钟过期
    tokenCache.access_token = access_token
    tokenCache.access_token_expires = now + (expires_in - 300) * 1000

    return access_token
  }
  catch (error) {
    console.error('获取access_token失败:', error)
    throw error
  }
}

/**
 * 获取jsapi_ticket
 */
async function getJsApiTicket() {
  const now = Date.now()

  // 如果缓存的ticket还有效，直接返回
  if (tokenCache.jsapi_ticket && now < tokenCache.jsapi_ticket_expires) {
    return tokenCache.jsapi_ticket
  }

  try {
    const accessToken = await getAccessToken()

    const response = await axios.get('https://api.weixin.qq.com/cgi-bin/ticket/getticket', {
      params: {
        type: 'jsapi',
        access_token: accessToken,
      },
    })

    const { ticket, expires_in } = response.data

    if (!ticket) {
      throw new Error(`获取jsapi_ticket失败: ${JSON.stringify(response.data)}`)
    }

    // 缓存ticket，提前5分钟过期
    tokenCache.jsapi_ticket = ticket
    tokenCache.jsapi_ticket_expires = now + (expires_in - 300) * 1000

    return ticket
  }
  catch (error) {
    console.error('获取jsapi_ticket失败:', error)
    throw error
  }
}

/**
 * 生成随机字符串
 */
function generateNonceStr(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成微信JS-SDK签名
 */
function generateSignature(ticket, nonceStr, timestamp, url) {
  // 参数排序并拼接
  const string1 = [
    `jsapi_ticket=${ticket}`,
    `noncestr=${nonceStr}`,
    `timestamp=${timestamp}`,
    `url=${url}`,
  ].join('&')

  // SHA1加密
  const signature = crypto.createHash('sha1').update(string1).digest('hex')

  return signature
}

/**
 * 获取微信JS-SDK配置
 */
app.post('/v1/wechat/js-sdk/config', async (req, res) => {
  try {
    const { url } = req.body

    if (!url) {
      return res.status(400).json({
        code: 400,
        msg: '缺少url参数',
        result: null,
      })
    }

    // 获取jsapi_ticket
    const ticket = await getJsApiTicket()

    // 生成签名参数
    const nonceStr = generateNonceStr()
    const timestamp = Math.floor(Date.now() / 1000)

    // 生成签名
    const signature = generateSignature(ticket, nonceStr, timestamp, url)

    // 返回配置
    const config = {
      appId: WECHAT_CONFIG.appId,
      timestamp,
      nonceStr,
      signature,
      jsApiList: [
        'checkJsApi',
        'scanQRCode',
        'chooseImage',
        'previewImage',
        'uploadImage',
        'downloadImage',
        'getLocation',
        'openLocation',
        'hideMenuItems',
        'showMenuItems',
        'hideAllNonBaseMenuItem',
        'showAllNonBaseMenuItem',
        'closeWindow',
      ],
    }

    res.json({
      code: 200,
      msg: '成功',
      result: config,
    })
  }
  catch (error) {
    console.error('获取微信配置失败:', error)
    res.status(500).json({
      code: 500,
      msg: `获取微信配置失败: ${error.message}`,
      result: null,
    })
  }
})

/**
 * 健康检查接口
 */
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    cache: {
      access_token_expires: new Date(tokenCache.access_token_expires).toISOString(),
      jsapi_ticket_expires: new Date(tokenCache.jsapi_ticket_expires).toISOString(),
    },
  })
})

/**
 * 清除缓存接口（用于调试）
 */
app.post('/v1/wechat/clear-cache', (req, res) => {
  tokenCache = {
    access_token: null,
    access_token_expires: 0,
    jsapi_ticket: null,
    jsapi_ticket_expires: 0,
  }

  res.json({
    code: 200,
    msg: '缓存已清除',
    result: null,
  })
})

const PORT = process.env.PORT || 3000
app.listen(PORT, () => {
  console.log(`微信JS-SDK服务已启动，端口: ${PORT}`)
  console.log(`健康检查: http://localhost:${PORT}/health`)
  console.log(`配置接口: POST http://localhost:${PORT}/v1/wechat/js-sdk/config`)
})

module.exports = app

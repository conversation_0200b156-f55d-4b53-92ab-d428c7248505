import type { EventCenterForMicroApp } from '@micro-zoe/micro-app'
import type { VITE_DYNAMIC_IMPORT_FUNC_NAME } from '../src/env'

declare global {
  interface Window {
    pinia: Pinia
    [VITE_DYNAMIC_IMPORT_FUNC_NAME]: (file: string) => string

    // QianKun 环境变量
    __POWERED_BY_QIANKUN__?: boolean
    __INJECTED_PUBLIC_PATH_BY_QIANKUN__?: string
    __QIANKUN_DEVELOPMENT__?: boolean

    // MicroApp 环境变量
    __MICRO_APP_ENVIRONMENT__: boolean
    __MICRO_APP_NAME__: string
    __MICRO_APP_URL__: string
    __MICRO_APP_PUBLIC_PATH__: string
    __MICRO_APP_BASE_URL__: string
    __MICRO_APP_BASE_ROUTE__: string
    __MICRO_APP_UMD_MODE__: boolean
    __MICRO_APP_PRE_RENDER__: boolean
    microApp: EventCenterForMicroApp
    rawWindow: Window
    rawDocument: Document
    removeDomScope: () => void

    /**
     * guc
     */
    __KEYCLOAK__: any
    /**
     * guc
     */
    __GEEGAGUC__: any

    /**
     * 微信JS-SDK
     */
    wx?: {
      config: (config: any) => void
      ready: (callback: () => void) => void
      error: (callback: (error: any) => void) => void
      checkJsApi: (config: any) => void
      scanQRCode: (config: any) => void
      chooseImage: (config: any) => void
      previewImage: (config: any) => void
      uploadImage: (config: any) => void
      downloadImage: (config: any) => void
      getLocation: (config: any) => void
      openLocation: (config: any) => void
      hideMenuItems: (config: any) => void
      showMenuItems: (config: any) => void
      hideAllNonBaseMenuItem: () => void
      showAllNonBaseMenuItem: () => void
      closeWindow: () => void
      [key: string]: any
    }

    /**
     * 运行时配置
     */
    __mkAppRuntimeConfig__?: {
      /**
       * 监控平台配置
       */
      monitor?: {
        appID?: string
        service?: string
      }
    }
  }

  // build date
  const __BUILD_TIMESTAMP__: number
}

/// <reference types="vite/client" />
/// <reference types="unplugin-icons/types/vue" />
/// <reference types="unplugin-vue-router/client" />


interface ImportMetaEnv {
  MK_APP_TITLE: string
  MK_APP_API_URL: string
  MK_APP_ALG_API_URL: string

  MK_APP_GUC_APPID: string
  MK_APP_GUC_API_URL: string

  MK_APP_ONLINE_MONITOR_URL: string
  MK_APP_ONLINE_MONITOR_SERVICE: string
  MK_APP_ONLINE_MONITOR_APPID: string

  /**
   * 是否是私有化部署
   */
  MK_APP_PRIVATE_DEPLOY: boolean

  MK_APP_PREVIEW_BASE: string
}

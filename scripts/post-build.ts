import { readFile, writeFile } from 'node:fs/promises'
import { Window } from 'happy-dom'

main()

async function main() {
  const html = await readFile('dist/index.html', { encoding: 'utf-8' })

  const window = new Window({
    settings: {
      disableJavaScriptEvaluation: true,
      disableCSSFileLoading: true,
    },
  })
  const document = window.document

  document.write(html)

  document.querySelectorAll('script').forEach((_item) => {
    const item = _item as any as HTMLScriptElement

    if (item.type === 'module' || (!item.src && !item.getAttribute('data-src'))) {
      item.remove()
    }

    if (item.hasAttribute('nomodule')) {
      item.removeAttribute('nomodule')
    }

    const legacyEntryId = 'vite-legacy-entry'
    if (item.id === legacyEntryId) {
      item.id = ''
      const systemJSInlineCode = `System.import((window.__MICRO_APP_PUBLIC_PATH__ || '')+document.getElementById('${legacyEntryId}').getAttribute('data-src'))`

      item.textContent = systemJSInlineCode

      // 兼容 micro-app
      const el = document.createElement('div')
      el.id = legacyEntryId
      el.setAttribute('data-src', item.getAttribute('data-src') || '')
      item.before(el as any as Node)
    }
  })

  const outerHtml = `<!DOCTYPE html>\n${document.documentElement.outerHTML}`

  await writeFile('dist/index.html', outerHtml)
}

FROM y2-harbor.geega.com:31104/library/nginx:alpine
# FROM nginx:alpine

COPY  /dist/ /usr/share/nginx/html/
COPY  /CICD/default.conf  /etc/nginx/conf.d/default.conf.template

ENV API=http://intelli-matching-service.caas-cloud-dev.geega.com/
ENV GUC=https://guc3-api-test.geega.com/
ENV MINIO=http://172.28.90.254:9000/

EXPOSE 80

WORKDIR /etc/nginx/conf.d
ENTRYPOINT envsubst '$PORT $API $GUC $MINIO'  < default.conf.template > default.conf && cat default.conf && nginx -g 'daemon off;'

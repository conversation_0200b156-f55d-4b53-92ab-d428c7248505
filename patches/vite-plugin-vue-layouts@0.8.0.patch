diff --git a/dist/index.js b/dist/index.js
index 171bae8668a069e669c1a42df5216ebdba7d9943..aad2d9eff737ab7a852ad08527abee90e3227e67 100644
--- a/dist/index.js
+++ b/dist/index.js
@@ -128,11 +128,11 @@ function layoutPlugin(userOptions = {}) {
       watcher.on("unlink", () => {
         updateVirtualModule();
       });
-      watcher.on("change", async (path) => {
-        path = `/${normalizePath(path)}`;
-        const module = await moduleGraph.getModuleByUrl(path);
-        reloadModule(module, path);
-      });
+      // watcher.on("change", async (path) => {
+      //   path = `/${normalizePath(path)}`;
+      //   const module = await moduleGraph.getModuleByUrl(path);
+      //   reloadModule(module, path);
+      // });
     },
     resolveId(id) {
       return MODULE_IDS.includes(id) || MODULE_IDS.some((i) => id.startsWith(i)) ? MODULE_ID_VIRTUAL : null;
diff --git a/dist/index.mjs b/dist/index.mjs
index a8e9153d0df64b8ca059b86e9aa9aeeb304f6058..3c29dbbce6a36e2a9273e9fa7f6acea1af19ab54 100644
--- a/dist/index.mjs
+++ b/dist/index.mjs
@@ -128,11 +128,11 @@ function layoutPlugin(userOptions = {}) {
       watcher.on("unlink", () => {
         updateVirtualModule();
       });
-      watcher.on("change", async (path) => {
-        path = `/${normalizePath(path)}`;
-        const module = await moduleGraph.getModuleByUrl(path);
-        reloadModule(module, path);
-      });
+      // watcher.on("change", async (path) => {
+      //   path = `/${normalizePath(path)}`;
+      //   const module = await moduleGraph.getModuleByUrl(path);
+      //   reloadModule(module, path);
+      // });
     },
     resolveId(id) {
       return MODULE_IDS.includes(id) || MODULE_IDS.some((i) => id.startsWith(i)) ? MODULE_ID_VIRTUAL : null;
# 运维平台中的负载均衡请不要打开跨域配置
# 因为这会导致 nginx 的配置被覆盖掉
server {
    listen 80;
    server_name  localhost;
    resolver 127.0.0.11 valid=1s;
	  
    gzip on;
	  gzip_comp_level 7;
    gzip_vary on;
    gzip_min_length 10m;
	  gzip_types video/* application/octet-stream;

    client_max_body_size 2048m;

    # 防止后端直接返回 Access-Control-Allow-Origin 导致报错
    proxy_hide_header Access-Control-Allow-Origin;

    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods *;
    add_header Access-Control-Allow-Headers *;
    add_header Access-Control-Allow-Credentials: true;
    add_header Access-Control-Allow-Headers: DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization;
    add_header Access-Control-Allow-Private-Network: true;

    location / {
      # 防止后端直接返回 Access-Control-Allow-Origin 导致报错
      proxy_hide_header Access-Control-Allow-Origin;

      add_header Access-Control-Allow-Origin *;
      add_header Access-Control-Allow-Methods *;

      root   /usr/share/nginx/html;
      index  index.html index.htm;
      try_files $uri $uri/ /index.html;
    }

    # 不缓存 html 文件
    location ~* \.(html)$ {
      # 防止后端直接返回 Access-Control-Allow-Origin 导致报错
      proxy_hide_header Access-Control-Allow-Origin;

      add_header Access-Control-Allow-Origin *;
      add_header Access-Control-Allow-Methods *;


      add_header Cache-Control 'no-store';
      add_header Cache-Control 'no-cache';
      expires 0;

      root   /usr/share/nginx/html;
      index  index.html index.htm;
      try_files $uri $uri/ /index.html;
    }


    # 监控平台
    location /api/monitor-collect/  {
      # 防止后端直接返回 Access-Control-Allow-Origin 导致报错
      proxy_hide_header Access-Control-Allow-Origin;

      add_header Access-Control-Allow-Origin *;
      add_header Access-Control-Allow-Methods *;

      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "Upgrade";
      proxy_set_header X-real-ip $remote_addr;
      proxy_set_header X-Forwarded-For $remote_addr;
      proxy_ssl_session_reuse off;
      proxy_ssl_server_name on;
      proxy_ssl_protocols TLSv1.2;

      # 监控平台地址，区分环境
      set $upstream https://onlinemonitor-server-dev.geega.com/;
      proxy_pass $upstream;
    }

    # GUC 地址
    location /gateway/guc {
      # 防止后端直接返回 Access-Control-Allow-Origin 导致报错
      proxy_hide_header Access-Control-Allow-Origin;

      add_header Access-Control-Allow-Origin *;
      add_header Access-Control-Allow-Methods *;
      add_header Access-Control-Allow-Headers *;

      if ($request_method = OPTIONS ) {
        return 200;
      }

      proxy_pass $GUC;
      # 关闭重定向 URL 的更改
      proxy_redirect off;
    }

    location /app-api/ {
      # 防止后端直接返回 Access-Control-Allow-Origin 导致报错
      proxy_hide_header Access-Control-Allow-Origin;

      add_header Access-Control-Allow-Origin *;
      add_header Access-Control-Allow-Methods *;
      add_header Access-Control-Allow-Headers *;

      if ($request_method = OPTIONS ) {
        return 200;
      }

      proxy_pass $API;
    }

    # minio 存储
    location /storage/ {
      # 防止后端直接返回 Access-Control-Allow-Origin 导致报错
      proxy_hide_header Access-Control-Allow-Origin;

      add_header Access-Control-Allow-Origin *;
      add_header Access-Control-Allow-Methods *;

      if ($request_uri ~* ^/storage/minio/.*) {return 403;}

      # 修改 minio 地址
      # proxy_pass  http://************:9090/;
      proxy_pass  $MINIO;

      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_connect_timeout 3600;
      proxy_max_temp_file_size 2048m;
      client_body_buffer_size 512k;
      client_max_body_size 1000m;
      proxy_buffering off;  
      proxy_buffers 4 64k;  
      proxy_buffer_size 16k;  
      proxy_busy_buffers_size 128k;  
      proxy_temp_file_write_size 128k;  

      # 对于200、206、304、301、302状态码的数据缓存1天  
      proxy_cache_valid 200 206 304 301 302 1d;  
      # 对于其他状态的数据缓存30分钟  
      proxy_cache_valid any 30m;  
      # 定义生成缓存键的规则（请求的url+参数作为key）  
      proxy_cache_key $host$uri$is_args$args;  
      # 资源至少被重复访问三次后再加入缓存  
      proxy_cache_min_uses 3;  
      # 出现重复请求时，只让一个去后端读数据，其他的从缓存中读取  
      proxy_cache_lock on;  
      # 上面的锁超时时间为3s，超过3s未获取数据，其他请求直接去后端  
      proxy_cache_lock_timeout 3s;  

      sendfile on;
      keepalive_timeout 3600;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}

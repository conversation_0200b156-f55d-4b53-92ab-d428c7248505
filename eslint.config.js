import antfu from '@antfu/eslint-config'

export default antfu(
  {
    unocss: true,
    formatters: true,
    vue: true,
    rules: {
      'no-console': 'off',
      'eslint-comments/no-unlimited-disable': 'off',
      'vue/custom-event-name-casing': 'off',
      'ts/no-use-before-define': 'off',
      'no-template-curly-in-string': 'off',
    },
    ignores: ['.history', 'public', 'scripts', 'assets', 'src/api/api.*'],
  },
)
